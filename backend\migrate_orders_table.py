#!/usr/bin/env python3
"""
Database migration script for order status management enhancements
"""

import sqlite3
import os
from datetime import datetime

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def migrate_orders_table():
    """Migrate the orders table to support enhanced status management"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("🔄 Migrating orders table for enhanced status management...")
        
        # Get current table structure
        cursor.execute("PRAGMA table_info(orders)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   Current columns: {', '.join(columns)}")
        
        # Add missing columns if they don't exist
        migrations = [
            ('billing_address', 'TEXT'),
            ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
            ('status_updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
            ('estimated_delivery', 'TIMESTAMP'),
            ('tracking_number', 'TEXT')
        ]
        
        for column_name, column_type in migrations:
            if column_name not in columns:
                try:
                    # For columns with DEFAULT, we need to handle them specially
                    if 'DEFAULT CURRENT_TIMESTAMP' in column_type:
                        # Add without default first, then update
                        cursor.execute(f'ALTER TABLE orders ADD COLUMN {column_name} TIMESTAMP')
                        # Update existing rows with current timestamp
                        cursor.execute(f'UPDATE orders SET {column_name} = CURRENT_TIMESTAMP WHERE {column_name} IS NULL')
                    else:
                        cursor.execute(f'ALTER TABLE orders ADD COLUMN {column_name} {column_type}')
                    
                    print(f"   ✅ Added column: {column_name}")
                except Exception as e:
                    print(f"   ⚠️ Column {column_name} might already exist or error: {e}")
        
        # Create order status history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                old_status TEXT,
                new_status TEXT NOT NULL,
                changed_by TEXT DEFAULT 'system',
                change_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders (id)
            )
        ''')
        print("   ✅ Created order_status_history table")
        
        # Update existing orders to have proper timestamps if they're missing
        cursor.execute('''
            UPDATE orders 
            SET updated_at = COALESCE(updated_at, created_at, CURRENT_TIMESTAMP),
                status_updated_at = COALESCE(status_updated_at, created_at, CURRENT_TIMESTAMP)
            WHERE updated_at IS NULL OR status_updated_at IS NULL
        ''')
        
        # Add billing address for existing orders if missing
        cursor.execute('''
            UPDATE orders 
            SET billing_address = COALESCE(billing_address, shipping_address)
            WHERE billing_address IS NULL
        ''')
        
        conn.commit()
        
        # Verify the migration
        cursor.execute("PRAGMA table_info(orders)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"   Updated columns: {', '.join(new_columns)}")
        
        # Check if we have any orders
        cursor.execute("SELECT COUNT(*) FROM orders")
        order_count = cursor.fetchone()[0]
        print(f"   Total orders in database: {order_count}")
        
        conn.close()
        
        print("✅ Orders table migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error migrating orders table: {e}")
        return False

def create_sample_order():
    """Create a sample order for testing"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get the first user
        cursor.execute('SELECT id, email FROM users LIMIT 1')
        user_result = cursor.fetchone()
        
        if not user_result:
            print("❌ No users found. Please create a user first.")
            return None
            
        user_id, user_email = user_result
        
        # Create a sample order
        order_number = f"GML-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        cursor.execute('''
            INSERT INTO orders (
                user_id, order_number, status, total_amount, 
                shipping_address, billing_address, phone, email, notes,
                created_at, updated_at, status_updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id, order_number, 'Pending', 89.97,
            '123 Medical Plaza, Healthcare City, HC 12345',
            '123 Medical Plaza, Healthcare City, HC 12345',
            '******-MEDICAL',
            user_email,
            'Sample order for testing enhanced status management',
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        order_id = cursor.lastrowid
        
        # Add sample order items
        sample_items = [
            (1, 2, 25.99, 51.98),  # 2x Disposable Syringes 10ml
            (3, 3, 12.99, 38.97)   # 3x Surgical Face Masks
        ]
        
        for product_id, quantity, unit_price, total_price in sample_items:
            cursor.execute('''
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (order_id, product_id, quantity, unit_price, total_price))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Sample order created successfully!")
        print(f"   Order ID: {order_id}")
        print(f"   Order Number: {order_number}")
        print(f"   Status: Pending")
        print(f"   Total: $89.97")
        
        return order_id
        
    except Exception as e:
        print(f"❌ Error creating sample order: {e}")
        return None

def show_orders():
    """Show current orders"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, order_number, status, total_amount, created_at, status_updated_at
            FROM orders
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        orders = cursor.fetchall()
        conn.close()
        
        if not orders:
            print("📦 No orders found in the database")
            return
        
        print(f"\n📋 Recent Orders:")
        print("-" * 80)
        print(f"{'ID':<4} {'Order Number':<20} {'Status':<12} {'Total':<10} {'Created':<20}")
        print("-" * 80)
        
        for order in orders:
            order_id, order_number, status, total_amount, created_at, status_updated_at = order
            created_date = datetime.fromisoformat(created_at).strftime('%Y-%m-%d %H:%M') if created_at else 'N/A'
            
            print(f"{order_id:<4} {order_number:<20} {status:<12} ${total_amount:<9.2f} {created_date:<20}")
        
    except Exception as e:
        print(f"❌ Error showing orders: {e}")

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Database Migration")
    print("=" * 50)
    
    # Run the migration
    if migrate_orders_table():
        print("\n📋 Current orders after migration:")
        show_orders()
        
        # Create a sample order if no orders exist
        print("\n🆕 Creating sample order for testing...")
        sample_order_id = create_sample_order()
        
        if sample_order_id:
            print("\n📋 Orders after creating sample:")
            show_orders()
    
    print("\n🎉 Migration completed! You can now test the enhanced order status management.")
