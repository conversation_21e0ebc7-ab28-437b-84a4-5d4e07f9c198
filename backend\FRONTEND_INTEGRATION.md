# Frontend Integration Guide

## 🔗 Backend API Base URL
```
http://127.0.0.1:5000
```

## 🚀 React Frontend Integration Examples

### 1. Basic API Service Setup

Create `src/services/api.js`:

```javascript
// API Configuration
const API_BASE_URL = 'http://127.0.0.1:5000';

// Create axios instance with default config
import axios from 'axios';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Set to true if using cookies
});

// Add request interceptor to include JWT token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

### 2. API Functions

```javascript
// Authentication
export const authAPI = {
  login: async (email, password) => {
    const response = await api.post('/api/auth/login', { email, password });
    return response.data;
  },
  
  register: async (userData) => {
    const response = await api.post('/api/auth/register', userData);
    return response.data;
  }
};

// Products
export const productsAPI = {
  getAll: async (params = {}) => {
    const response = await api.get('/api/products', { params });
    return response.data;
  },
  
  getById: async (id) => {
    const response = await api.get(`/api/products/${id}`);
    return response.data;
  },
  
  getCategories: async () => {
    const response = await api.get('/api/categories');
    return response.data;
  }
};

// Orders
export const ordersAPI = {
  getAll: async (params = {}) => {
    const response = await api.get('/api/orders', { params });
    return response.data;
  },
  
  create: async (orderData) => {
    const response = await api.post('/api/orders', orderData);
    return response.data;
  },
  
  getById: async (id) => {
    const response = await api.get(`/api/orders/${id}`);
    return response.data;
  }
};

// Admin (requires API key)
export const adminAPI = {
  getAllOrders: async (params = {}) => {
    const response = await api.get('/api/admin/orders', {
      params,
      headers: { 'X-API-Key': '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' }
    });
    return response.data;
  },
  
  getReports: async (params = {}) => {
    const response = await api.get('/api/admin/reports', {
      params,
      headers: { 'X-API-Key': '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' }
    });
    return response.data;
  }
};
```

### 3. React Component Examples

```jsx
// Login Component
import React, { useState } from 'react';
import { authAPI } from '../services/api';

const LoginForm = () => {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      const response = await authAPI.login(credentials.email, credentials.password);
      localStorage.setItem('access_token', response.access_token);
      localStorage.setItem('user', JSON.stringify(response.user));
      // Redirect to dashboard
      window.location.href = '/dashboard';
    } catch (error) {
      setError(error.response?.data?.error || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleLogin}>
      <input
        type="email"
        placeholder="Email"
        value={credentials.email}
        onChange={(e) => setCredentials({...credentials, email: e.target.value})}
        required
      />
      <input
        type="password"
        placeholder="Password"
        value={credentials.password}
        onChange={(e) => setCredentials({...credentials, password: e.target.value})}
        required
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
      {error && <div className="error">{error}</div>}
    </form>
  );
};

// Products Component
import React, { useState, useEffect } from 'react';
import { productsAPI } from '../services/api';

const ProductsList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await productsAPI.getAll();
        setProducts(response.products);
      } catch (error) {
        setError('Failed to fetch products');
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Products ({products.length})</h2>
      <div className="products-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <h3>{product.name}</h3>
            <p>{product.description}</p>
            <p>Price: ${product.unit_price}</p>
            <p>Stock: {product.stock_quantity}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### 4. Fetch API Alternative (No Axios)

```javascript
// Using native fetch API
const fetchProducts = async () => {
  try {
    const response = await fetch('http://127.0.0.1:5000/api/products', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Products:', data.products);
    return data;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
};

// Login with fetch
const login = async (email, password) => {
  try {
    const response = await fetch('http://127.0.0.1:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Login failed');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};
```

## 🔧 Debugging CORS Issues

### Common Issues & Solutions:

1. **CORS Preflight Errors**
   - Backend now handles OPTIONS requests automatically
   - Check browser dev tools Network tab for preflight requests

2. **Mixed Content (HTTP/HTTPS)**
   - Ensure both frontend and backend use same protocol
   - Use http://127.0.0.1:5000 (not https)

3. **Port Conflicts**
   - Frontend: Usually runs on 3000 or 5173
   - Backend: Runs on 5000
   - Make sure ports don't conflict

4. **Network Errors**
   - Check if backend is running: `curl http://127.0.0.1:5000/api/health`
   - Verify firewall/antivirus isn't blocking requests

### Browser Dev Tools Debugging:
1. Open Network tab
2. Look for failed requests (red entries)
3. Check request headers and response
4. Look for CORS-related error messages in Console

## 🎯 Test Credentials
- **Email:** <EMAIL>
- **Password:** admin123
- **API Key:** 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
