#!/usr/bin/env python3
"""
Final fix for image URLs - Replace all problematic URLs with proper medical product images
"""

import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def fix_all_image_urls():
    """Fix all image URLs with proper medical product images"""
    
    # Direct mapping of product names to proper image URLs
    product_image_mapping = {
        'Disposable Syringes 10ml': 'https://www.medicalexpo.com/prod/changzhou-medical-appliances-general-factory/product-68000-990637.html',
        'Nitrile Examination Gloves': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop',
        'Surgical Face Masks': 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop',
        'Digital Thermometer': 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=300&h=200&fit=crop',
        'Antiseptic Solution 500ml': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
        'Surgical Scissors': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
        'Gauze Bandages 4x4': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
        'Alcohol Swabs': 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop',
        'Blood Pressure Cuff': 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'
    }
    
    # Category-based fallback images
    category_images = {
        'syringe': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
        'glove': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop',
        'mask': 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop',
        'thermometer': 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=300&h=200&fit=crop',
        'antiseptic': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
        'solution': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
        'scissors': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
        'bandage': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
        'gauze': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
        'swab': 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop',
        'alcohol': 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop',
        'pressure': 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop',
        'cuff': 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop',
        'stethoscope': 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'
    }
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get all products
        cursor.execute('SELECT id, name, image_url FROM products ORDER BY id')
        products = cursor.fetchall()
        
        print("🔄 Fixing all image URLs...")
        print("=" * 80)
        
        updated_count = 0
        
        for product_id, product_name, current_url in products:
            new_url = None
            
            # First, check for exact name match
            for target_name, image_url in product_image_mapping.items():
                if target_name.lower() in product_name.lower() or product_name.lower() in target_name.lower():
                    new_url = image_url
                    break
            
            # If no exact match, use category-based matching
            if not new_url:
                product_name_lower = product_name.lower()
                for keyword, image_url in category_images.items():
                    if keyword in product_name_lower:
                        new_url = image_url
                        break
            
            # Default fallback
            if not new_url:
                new_url = 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
            
            # Update the product
            cursor.execute('UPDATE products SET image_url = ? WHERE id = ?', (new_url, product_id))
            
            print(f"✅ ID {product_id:2d}: {product_name[:40]:40} -> {new_url}")
            updated_count += 1
        
        conn.commit()
        conn.close()
        
        print("=" * 80)
        print(f"🎉 Successfully updated {updated_count} product images!")
        print("All products now have proper medical product images.")
        
    except Exception as e:
        print(f"❌ Error updating images: {str(e)}")

def replace_problematic_urls():
    """Replace any remaining problematic URLs"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Replace any placeholder URLs
        cursor.execute('''
            UPDATE products 
            SET image_url = 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
            WHERE image_url LIKE '%placeholder%' OR image_url LIKE '%via.placeholder%'
        ''')
        
        # Replace the old problematic URL
        cursor.execute('''
            UPDATE products 
            SET image_url = 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
            WHERE image_url = 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ Replaced all problematic URLs")
        
    except Exception as e:
        print(f"❌ Error replacing URLs: {str(e)}")

def verify_image_urls():
    """Verify all image URLs are properly set"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, image_url FROM products ORDER BY id LIMIT 10')
        products = cursor.fetchall()
        
        print("\n📋 Verification - First 10 Products:")
        print("-" * 80)
        
        for product_id, name, image_url in products:
            print(f"ID {product_id:2d}: {name[:35]:35} | {image_url}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying URLs: {str(e)}")

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Final Image URL Fix")
    print("=" * 80)
    
    # Step 1: Fix all image URLs
    fix_all_image_urls()
    
    # Step 2: Replace any remaining problematic URLs
    replace_problematic_urls()
    
    # Step 3: Verify the changes
    verify_image_urls()
    
    print("\n" + "=" * 80)
    print("✅ Image URL fix completed!")
    print("All products should now have proper medical product images.")
