#!/usr/bin/env python3
"""
Check product images in the database
"""

import sqlite3
import os
import json

def check_images():
    """Check product images in the database"""
    db_path = os.path.join(os.path.dirname(__file__), 'grace_medical.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print("🔍 Checking product images in database...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check total products
        cursor.execute('SELECT COUNT(*) FROM products')
        total_products = cursor.fetchone()[0]
        print(f"📦 Total products: {total_products}")
        
        # Check products with missing images
        cursor.execute('''
            SELECT COUNT(*) FROM products 
            WHERE image_url IS NULL OR image_url = '' OR image_url = 'null'
        ''')
        missing_images = cursor.fetchone()[0]
        print(f"🖼️ Products with missing images: {missing_images}")
        
        # Show sample products with their image URLs
        cursor.execute('''
            SELECT id, name, image_url FROM products 
            LIMIT 5
        ''')
        
        sample_products = cursor.fetchall()
        print(f"\n📋 Sample products:")
        for product_id, name, image_url in sample_products:
            print(f"  ID {product_id}: {name}")
            print(f"    Image URL: {image_url or 'NULL'}")
            print()
        
        # Check orders table schema
        print("📋 Orders table schema:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def test_api_response():
    """Test the API response format"""
    import requests
    
    try:
        print("\n🌐 Testing API response...")
        response = requests.get('http://127.0.0.1:5000/api/products?per_page=2')
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response Status: {response.status_code}")
            print(f"📦 Products returned: {len(data.get('products', []))}")
            
            if data.get('products'):
                first_product = data['products'][0]
                print(f"\n📋 First product sample:")
                print(f"  Name: {first_product.get('name')}")
                print(f"  Image URL: {first_product.get('image_url')}")
                print(f"  Category: {first_product.get('category')}")
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API Test Error: {e}")

if __name__ == "__main__":
    print("🔧 Grace Medical Limited - Image Check")
    print("=" * 40)
    
    # Check database
    db_success = check_images()
    
    # Test API
    if db_success:
        test_api_response()
    
    print("\n" + "=" * 40)
