#!/usr/bin/env python3
"""
Update product images with real medical product photos
"""
import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def get_medical_image(product_name, product_id):
    """Get real medical product image URL with better matching"""

    name_lower = product_name.lower()

    # High-quality medical images from Unsplash with better categorization

    # Syringes and Injection Equipment
    if 'syringe' in name_lower or 'injection' in name_lower or 'needle' in name_lower:
        images = [
            'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&auto=format',  # Medical syringes
            'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&auto=format',  # Syringe close-up
            'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop&auto=format'   # Medical injection
        ]
        return images[product_id % len(images)]

    # Gloves - Medical/Examination
    elif 'glove' in name_lower and ('nitrile' in name_lower or 'examination' in name_lower or 'medical' in name_lower):
        images = [
            'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop&auto=format',  # Blue nitrile gloves
            'https://images.unsplash.com/photo-1585435557343-3b092031d4c1?w=400&h=300&fit=crop&auto=format',  # Medical gloves
            'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=400&h=300&fit=crop&auto=format'   # Examination gloves
        ]
        return images[product_id % len(images)]

    # Surgical/Face Masks
    elif 'mask' in name_lower and ('surgical' in name_lower or 'face' in name_lower or 'medical' in name_lower):
        images = [
            'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=400&h=300&fit=crop&auto=format',  # Surgical masks
            'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?w=400&h=300&fit=crop&auto=format',  # Face masks stack
            'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=400&h=300&fit=crop&auto=format'   # Medical masks
        ]
        return images[product_id % len(images)]

    # Thermometers
    elif 'thermometer' in name_lower or 'temperature' in name_lower:
        return 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=400&h=300&fit=crop&auto=format'

    # Stethoscopes
    elif 'stethoscope' in name_lower:
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop&auto=format'

    # Surgical Instruments
    elif 'scissors' in name_lower or 'forceps' in name_lower or 'clamp' in name_lower or 'scalpel' in name_lower:
        images = [
            'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&auto=format',  # Surgical instruments
            'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&auto=format'   # Medical tools
        ]
        return images[product_id % len(images)]

    # Bandages and Wound Care
    elif 'bandage' in name_lower or 'gauze' in name_lower or 'dressing' in name_lower:
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop&auto=format'

    # Medical Tape
    elif 'tape' in name_lower and ('medical' in name_lower or 'surgical' in name_lower):
        return 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=400&h=300&fit=crop&auto=format'

    # Antiseptics and Solutions
    elif 'antiseptic' in name_lower or 'alcohol' in name_lower or 'solution' in name_lower or 'disinfectant' in name_lower:
        return 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop&auto=format'

    # Swabs and Wipes
    elif 'swab' in name_lower or 'wipe' in name_lower or 'cotton' in name_lower:
        return 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=400&h=300&fit=crop&auto=format'

    # Monitoring Equipment
    elif 'oximeter' in name_lower or 'pressure' in name_lower or 'cuff' in name_lower or 'monitor' in name_lower:
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&auto=format'

    # Protective Equipment
    elif 'gown' in name_lower or 'cap' in name_lower or 'shield' in name_lower or 'protective' in name_lower:
        return 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=400&h=300&fit=crop&auto=format'

    # Diagnostic Equipment
    elif 'otoscope' in name_lower or 'scope' in name_lower or 'diagnostic' in name_lower:
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop&auto=format'

    # Pills, Tablets, Medications
    elif 'tablet' in name_lower or 'pill' in name_lower or 'capsule' in name_lower or 'medication' in name_lower:
        images = [
            'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop&auto=format',  # Pills and tablets
            'https://images.unsplash.com/photo-**********-c3190ca9959b?w=400&h=300&fit=crop&auto=format',   # Medication capsules
            'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop&auto=format'   # Pharmaceutical pills
        ]
        return images[product_id % len(images)]

    else:
        # Default medical equipment image
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop&auto=format'

def show_current_products():
    """Show current products and their image URLs"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT id, name, category, image_url FROM products ORDER BY id')
        products = cursor.fetchall()

        print(f"\n📋 Current Products ({len(products)} total):")
        print("=" * 80)

        for product_id, name, category, image_url in products:
            print(f"ID: {product_id:2d} | {name[:40]:40} | {category or 'No Category':15} | {image_url or 'No Image'}")

        conn.close()

    except Exception as e:
        print(f"Error reading products: {str(e)}")

def main():
    """Update all product images with better matching"""
    print("🏥 Grace Medical Limited - Image Update Tool")
    print("=" * 50)

    # Show current products first
    show_current_products()

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT id, name, category FROM products ORDER BY id')
        products = cursor.fetchall()

        print(f"\n🔄 Updating {len(products)} products with properly matched medical images...")
        print("-" * 60)

        updated_count = 0
        for product_id, name, category in products:
            new_url = get_medical_image(name, product_id)

            cursor.execute('UPDATE products SET image_url = ? WHERE id = ?', (new_url, product_id))

            print(f"✅ ID {product_id:2d}: {name[:35]:35} -> Updated")
            print(f"   Category: {category or 'None':15} | New Image: {new_url}")

            updated_count += 1

        conn.commit()
        conn.close()

        print(f"\n🎉 Successfully updated {updated_count} product images!")
        print("   All products now have properly matched medical images.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
