#!/usr/bin/env python3
"""
Update product images with real medical product photos
"""
import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def get_medical_image(product_name, product_id):
    """Get real medical product image URL"""
    
    name_lower = product_name.lower()
    
    # Professional medical images from Unsplash
    if 'syringe' in name_lower:
        images = [
            'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
            'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop',
            'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop'
        ]
        return images[product_id % len(images)]
    
    elif 'glove' in name_lower:
        images = [
            'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop',
            'https://images.unsplash.com/photo-1585435557343-3b092031d4c1?w=300&h=200&fit=crop',
            'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop'
        ]
        return images[product_id % len(images)]
    
    elif 'mask' in name_lower:
        images = [
            'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop',
            'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?w=300&h=200&fit=crop'
        ]
        return images[product_id % len(images)]
    
    elif 'thermometer' in name_lower:
        return 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=300&h=200&fit=crop'
    
    elif 'stethoscope' in name_lower:
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'
    
    elif 'scissors' in name_lower or 'forceps' in name_lower or 'clamp' in name_lower:
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
    
    elif 'bandage' in name_lower or 'gauze' in name_lower:
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'
    
    elif 'tape' in name_lower:
        return 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop'
    
    elif 'antiseptic' in name_lower or 'alcohol' in name_lower or 'solution' in name_lower:
        return 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop'
    
    elif 'swab' in name_lower or 'wipe' in name_lower:
        return 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop'
    
    elif 'oximeter' in name_lower or 'pressure' in name_lower or 'cuff' in name_lower:
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
    
    elif 'gown' in name_lower or 'cap' in name_lower or 'shield' in name_lower:
        return 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop'
    
    elif 'otoscope' in name_lower or 'scope' in name_lower:
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'
    
    else:
        # Default medical image
        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop'

def main():
    """Update all product images"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name FROM products')
        products = cursor.fetchall()
        
        print(f"Updating {len(products)} products with real medical images...")
        
        for product_id, name in products:
            new_url = get_medical_image(name, product_id)
            
            cursor.execute('UPDATE products SET image_url = ? WHERE id = ?', (new_url, product_id))
            print(f"Updated {product_id}: {name}")
        
        conn.commit()
        conn.close()
        
        print("All images updated successfully!")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
