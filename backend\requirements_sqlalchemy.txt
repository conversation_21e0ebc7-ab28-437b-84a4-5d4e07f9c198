# Grace Medical Limited - SQLAlchemy Version Requirements

# Core Flask and extensions
Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5
Flask-CORS==4.0.0

# Database
SQLAlchemy==2.0.23

# Security and authentication
Werkzeug==3.0.1
PyJWT==2.8.0

# Email (optional for production)
Flask-Mail==0.9.1

# Development and testing
pytest==7.4.3
pytest-flask==1.3.0

# CSV handling (built-in)
# datetime (built-in)
# uuid (built-in)
# os (built-in)

# Optional: For production deployment
# gunicorn==21.2.0
# python-dotenv==1.0.0

# Optional: For PDF generation (if needed)
# reportlab==4.0.7
# weasyprint==60.2
