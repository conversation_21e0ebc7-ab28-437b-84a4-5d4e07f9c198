# Grace Medical Limited - SQLAlchemy Implementation

A comprehensive medical supply e-commerce application built with Flask and SQLAlchemy ORM.

## 🏗️ Architecture Overview

### Database Models (SQLAlchemy ORM)

```
User (Authentication & User Management)
├── id (Primary Key)
├── email (Unique, Indexed)
├── password_hash (Secure)
├── first_name, last_name
├── phone, is_admin, is_verified
└── orders (Relationship to Order)

Product (Medical Supply Catalog)
├── id (Primary Key)
├── name, description, category
├── manufacturer, packaging
├── unit_price, bulk_price
├── stock_quantity, reorder_level
├── image_url, expiry_date
├── is_active (Soft Delete)
└── order_items (Relationship to OrderItem)

Order (Customer Orders)
├── id (Primary Key)
├── order_number (Unique, Auto-generated)
├── user_id (Foreign Key to User)
├── status, total_amount
├── shipping_address, phone, notes
├── created_at, updated_at
└── order_items (Relationship to OrderItem)

OrderItem (Order Line Items)
├── id (Primary Key)
├── order_id (Foreign Key to Order)
├── product_id (Foreign Key to Product)
├── quantity, unit_price, total_price
└── created_at

Category (Product Categories)
├── id (Primary Key)
├── name (Unique), description
├── is_active
└── products (Relationship to Product)

Notification (System Alerts)
├── id (Primary Key)
├── user_id (Foreign Key to User)
├── title, message, type
├── is_read
└── created_at
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements_sqlalchemy.txt
```

### 2. Run the Application

```bash
python app_sqlalchemy.py
```

### 3. Test the Setup

```bash
python test_sqlalchemy.py
```

## 📁 File Structure

```
backend/
├── app_sqlalchemy.py      # Main Flask application with SQLAlchemy
├── database.py            # SQLAlchemy models and database setup
├── config.py              # Configuration settings
├── migrate_to_sqlalchemy.py # Migration script from SQLite
├── test_sqlalchemy.py     # Test suite
├── requirements_sqlalchemy.txt # Dependencies
└── README_SQLAlchemy.md   # This file
```

## 🔧 Key Features

### ✅ SQLAlchemy ORM Benefits

1. **Object-Relational Mapping**: Clean Python classes for database tables
2. **Relationship Management**: Automatic handling of foreign keys and joins
3. **Query Builder**: Powerful and readable query construction
4. **Migration Support**: Database schema versioning with Flask-Migrate
5. **Data Validation**: Built-in validation and type checking
6. **Connection Pooling**: Efficient database connection management

### ✅ Advanced Database Features

1. **Proper Relationships**: One-to-many, many-to-one relationships
2. **Cascade Operations**: Automatic cleanup of related records
3. **Indexing**: Optimized queries with database indexes
4. **Soft Deletes**: is_active flags instead of hard deletes
5. **Timestamps**: Automatic created_at and updated_at tracking
6. **Data Integrity**: Foreign key constraints and validations

### ✅ Business Logic

1. **User Authentication**: Secure password hashing with Werkzeug
2. **Order Management**: Complete order lifecycle tracking
3. **Stock Management**: Low stock alerts and reorder levels
4. **Product Catalog**: Full-featured product management
5. **Email Notifications**: Order confirmations and stock alerts
6. **API Endpoints**: RESTful API for all operations

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login

### Products
- `GET /api/products` - List products (with pagination, search, filters)
- `GET /api/products/<id>` - Get single product

### Orders
- `POST /api/orders` - Create new order
- `GET /api/orders` - List orders (with pagination)
- `GET /api/orders/<id>` - Get single order
- `GET /api/orders/<id>/invoice` - Download invoice

### Admin
- `GET /api/admin/stock-alerts` - Get stock alerts
- `POST /api/admin/run-daily-cron` - Trigger daily checks

### Utilities
- `GET /api/categories` - List product categories

## 🗄️ Database Operations

### Query Examples

```python
# Find low stock products
low_stock = Product.query.filter(
    Product.stock_quantity <= Product.reorder_level
).all()

# Get user's orders with items
user_orders = Order.query.filter_by(user_id=user_id)\
    .options(db.joinedload(Order.order_items))\
    .all()

# Search products
products = Product.query.filter(
    db.or_(
        Product.name.ilike(f"%{search}%"),
        Product.description.ilike(f"%{search}%")
    )
).paginate(page=1, per_page=20)
```

### Model Methods

```python
# User password handling
user.set_password('password123')
user.check_password('password123')

# Product stock checking
product.is_low_stock  # Property
product.is_near_expiry()  # Method

# Data serialization
user_dict = user.to_dict()
order_dict = order.to_dict(include_items=True)
```

## 🔄 Migration from SQLite

If you have an existing SQLite database:

```bash
python migrate_to_sqlalchemy.py
```

This will:
1. Create a backup of your existing database
2. Migrate all data to the new SQLAlchemy format
3. Preserve all relationships and data integrity

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_sqlalchemy.py
```

Tests include:
- Database connection and table creation
- Model operations (CRUD)
- Relationship integrity
- API endpoint functionality
- Data validation

## 📊 Performance Optimizations

1. **Database Indexes**: On frequently queried fields
2. **Lazy Loading**: Efficient relationship loading
3. **Connection Pooling**: Reuse database connections
4. **Query Optimization**: Use joinedload for related data
5. **Pagination**: Limit large result sets

## 🔒 Security Features

1. **Password Hashing**: Werkzeug secure password hashing
2. **SQL Injection Protection**: SQLAlchemy ORM prevents injection
3. **Input Validation**: Model-level validation
4. **API Key Protection**: Admin routes require API key
5. **CORS Configuration**: Controlled cross-origin requests

## 🚀 Production Deployment

### Environment Variables

```bash
export FLASK_ENV=production
export DATABASE_URL=postgresql://user:pass@host:port/dbname
export SECRET_KEY=your-secret-key
export API_KEY=your-api-key
```

### Database Migration

```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

## 📈 Monitoring & Maintenance

1. **Daily Cron Jobs**: Automated stock and expiry checks
2. **Email Alerts**: Notifications for critical events
3. **Database Backups**: Regular automated backups
4. **Performance Monitoring**: Query performance tracking
5. **Error Logging**: Comprehensive error tracking

## 🎯 Next Steps

1. **Frontend Integration**: Connect with React/Vue.js frontend
2. **Payment Processing**: Integrate payment gateways
3. **Inventory Management**: Advanced stock tracking
4. **Reporting**: Business intelligence and analytics
5. **Mobile App**: REST API ready for mobile development

---

**Grace Medical Limited SQLAlchemy Implementation**  
*Professional medical supply e-commerce platform with enterprise-grade database architecture*
