#!/usr/bin/env python3
"""
Sc<PERSON><PERSON> to update all product images to use Picsum photos
"""
import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def get_medical_image_url(product_name, category):
    """Generate medical-relevant placeholder image URL based on product type"""

    # Medical product image mappings
    medical_images = {
        # Syringes and Injections
        'syringe': 'https://via.placeholder.com/300x200/e8f4fd/1e40af?text=Medical+Syringe',
        'injection': 'https://via.placeholder.com/300x200/e8f4fd/1e40af?text=Injection',

        # Gloves and PPE
        'glove': 'https://via.placeholder.com/300x200/f0fdf4/16a34a?text=Medical+Gloves',
        'mask': 'https://via.placeholder.com/300x200/fef3c7/d97706?text=Medical+Mask',
        'gown': 'https://via.placeholder.com/300x200/f3e8ff/7c3aed?text=Medical+Gown',
        'shield': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=Face+Shield',
        'cap': 'https://via.placeholder.com/300x200/f0f9ff/0284c7?text=Medical+Cap',
        'cover': 'https://via.placeholder.com/300x200/f8fafc/475569?text=Protective+Cover',

        # Instruments and Tools
        'thermometer': 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=Thermometer',
        'stethoscope': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=Stethoscope',
        'scissors': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Scissors',
        'forceps': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Forceps',
        'clamp': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Clamp',
        'scalpel': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Scalpel',
        'needle': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Needle',
        'probe': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Medical+Probe',
        'retractor': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Retractor',
        'elevator': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Elevator',
        'curette': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Bone+Curette',
        'knife': 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Knife',

        # Monitoring Equipment
        'oximeter': 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=📊+Pulse+Oximeter',
        'monitor': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=📺+Medical+Monitor',
        'pressure': 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=🩸+Blood+Pressure',
        'cuff': 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=🩸+BP+Cuff',
        'scale': 'https://via.placeholder.com/300x200/f8fafc/374151?text=⚖️+Digital+Scale',
        'electrode': 'https://via.placeholder.com/300x200/fef3c7/d97706?text=⚡+ECG+Electrode',

        # Examination Tools
        'otoscope': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=👂+Otoscope',
        'ophthalmoscope': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=👁️+Ophthalmoscope',
        'laryngoscope': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=👄+Laryngoscope',
        'speculum': 'https://via.placeholder.com/300x200/f8fafc/374151?text=🔍+Medical+Speculum',
        'hammer': 'https://via.placeholder.com/300x200/f8fafc/374151?text=🔨+Reflex+Hammer',
        'fork': 'https://via.placeholder.com/300x200/f8fafc/374151?text=🎵+Tuning+Fork',
        'penlight': 'https://via.placeholder.com/300x200/fef3c7/d97706?text=🔦+Medical+Penlight',
        'depressor': 'https://via.placeholder.com/300x200/f8fafc/374151?text=👅+Tongue+Depressor',

        # Bandages and Dressings
        'bandage': 'https://via.placeholder.com/300x200/f0fdf4/16a34a?text=🩹+Medical+Bandage',
        'gauze': 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=🩹+Gauze+Bandage',
        'tape': 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=📏+Medical+Tape',
        'dressing': 'https://via.placeholder.com/300x200/f0fdf4/16a34a?text=🩹+Wound+Dressing',

        # Solutions and Antiseptics
        'antiseptic': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Antiseptic',
        'alcohol': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Alcohol+Solution',
        'iodine': 'https://via.placeholder.com/300x200/fef3c7/d97706?text=🧴+Iodine+Solution',
        'hydrogen': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Hydrogen+Peroxide',
        'betadine': 'https://via.placeholder.com/300x200/fef3c7/d97706?text=🧴+Betadine',
        'chlorhexidine': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Chlorhexidine',
        'saline': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=🧴+Saline+Solution',
        'sanitizer': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Hand+Sanitizer',
        'cleanser': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Wound+Cleanser',
        'scrub': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Surgical+Scrub',
        'prep': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Skin+Prep',
        'wash': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Wound+Wash',
        'foam': 'https://via.placeholder.com/300x200/ecfdf5/059669?text=🧴+Antiseptic+Foam',
        'water': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=🧴+Sterile+Water',

        # Swabs and Wipes
        'swab': 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=🧽+Medical+Swab',
        'wipe': 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=🧽+Medical+Wipe',
        'pad': 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=🧽+Medical+Pad',

        # Respiratory Equipment
        'nebulizer': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=💨+Nebulizer+Mask',
        'oxygen': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=💨+Oxygen+Mask',
        'defibrillator': 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=⚡+Defibrillator+Pad',

        # Sutures
        'suture': 'https://via.placeholder.com/300x200/f8fafc/374151?text=🧵+Surgical+Suture',

        # Default medical
        'default': 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=🏥+Medical+Supply'
    }

    # Convert to lowercase for matching
    name_lower = product_name.lower()

    # Find matching image based on product name keywords
    for keyword, image_url in medical_images.items():
        if keyword in name_lower:
            return image_url

    # Default medical supply image
    return medical_images['default']

def update_product_images():
    """Update all product images to use medical-relevant placeholder images"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get all products with category
        cursor.execute('SELECT id, name, category, image_url FROM products')
        products = cursor.fetchall()

        print(f"🏥 Updating images for {len(products)} medical products...")

        for product_id, name, category, current_url in products:
            # Create medical-relevant image URL
            new_url = get_medical_image_url(name, category)

            # Update the product
            cursor.execute('''
                UPDATE products
                SET image_url = ?
                WHERE id = ?
            ''', (new_url, product_id))

            print(f"  ✅ Updated product {product_id}: {name}")
            print(f"     Category: {category}")
            print(f"     New: {new_url}")

        conn.commit()
        conn.close()

        print("🎉 All product images updated with medical-relevant placeholders!")

    except Exception as e:
        print(f"❌ Error updating images: {str(e)}")

if __name__ == "__main__":
    update_product_images()
