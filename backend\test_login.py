#!/usr/bin/env python3
"""
Test login functionality for both admin and regular users
"""

import sqlite3
import os
import requests
import json
from werkzeug.security import check_password_hash

DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def test_admin_login():
    """Test admin login functionality"""
    print("🔐 Testing Admin Login")
    print("=" * 40)
    
    # Test database password verification
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, email, password_hash, first_name, last_name, is_admin, is_verified
            FROM users WHERE email = ?
        ''', ('<EMAIL>',))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            print(f"✅ Admin user found in database")
            print(f"   Email: {user[1]}")
            print(f"   Name: {user[3]} {user[4]}")
            print(f"   Is Admin: {bool(user[5])}")
            print(f"   Is Verified: {bool(user[6])}")
            
            # Test password hash
            password_valid = check_password_hash(user[2], 'admin123')
            print(f"   Password Valid: {password_valid}")
            
            if not password_valid:
                print("❌ Password hash verification failed!")
                return False
                
        else:
            print("❌ Admin user not found in database")
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    
    # Test API login
    try:
        print(f"\n🌐 Testing API login...")
        response = requests.post('http://127.0.0.1:5000/api/auth/login', 
                               json={
                                   'email': '<EMAIL>',
                                   'password': 'admin123'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API login successful!")
            print(f"   User: {data['user']['first_name']} {data['user']['last_name']}")
            print(f"   Is Admin: {data['user']['is_admin']}")
            print(f"   Token: {data['access_token'][:20]}...")
            return True
        else:
            print(f"❌ API login failed: {response.status_code}")
            print(f"   Error: {response.json().get('error', 'Unknown error')}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the server is running.")
        return False
    except Exception as e:
        print(f"❌ API error: {e}")
        return False

def test_regular_user_login():
    """Test regular user login functionality"""
    print("\n👤 Testing Regular User Login")
    print("=" * 40)
    
    # Check if there are any regular users
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, email, first_name, last_name, is_admin, is_verified
            FROM users WHERE is_admin = 0 AND is_verified = 1
            LIMIT 1
        ''', )
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            print(f"✅ Regular user found: {user[1]}")
            print(f"   Name: {user[2]} {user[3]}")
            print(f"   Is Verified: {bool(user[5])}")
            return True
        else:
            print("ℹ️ No verified regular users found")
            print("   This is normal for a fresh installation")
            return True
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def create_test_user():
    """Create a test regular user for testing"""
    print("\n🆕 Creating Test User")
    print("=" * 40)
    
    try:
        response = requests.post('http://127.0.0.1:5000/api/auth/register', 
                               json={
                                   'first_name': 'Test',
                                   'last_name': 'User',
                                   'email': '<EMAIL>',
                                   'phone': '******-TEST',
                                   'password': 'test123'
                               },
                               timeout=10)
        
        if response.status_code == 201:
            print("✅ Test user created successfully!")
            
            # Manually verify the user in database (since email verification is mocked)
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute('UPDATE users SET is_verified = 1 WHERE email = ?', ('<EMAIL>',))
            conn.commit()
            conn.close()
            print("✅ Test user verified in database")
            
            # Test login
            print("\n🔐 Testing test user login...")
            login_response = requests.post('http://127.0.0.1:5000/api/auth/login', 
                                         json={
                                             'email': '<EMAIL>',
                                             'password': 'test123'
                                         },
                                         timeout=10)
            
            if login_response.status_code == 200:
                data = login_response.json()
                print(f"✅ Test user login successful!")
                print(f"   User: {data['user']['first_name']} {data['user']['last_name']}")
                print(f"   Is Admin: {data['user']['is_admin']}")
                return True
            else:
                print(f"❌ Test user login failed: {login_response.status_code}")
                print(f"   Error: {login_response.json().get('error', 'Unknown error')}")
                return False
                
        else:
            print(f"❌ Test user creation failed: {response.status_code}")
            print(f"   Error: {response.json().get('error', 'Unknown error')}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the server is running.")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Login System Test")
    print("=" * 60)
    
    # Test admin login
    admin_success = test_admin_login()
    
    # Test regular user functionality
    regular_success = test_regular_user_login()
    
    # Create and test a regular user
    test_user_success = create_test_user()
    
    print(f"\n📊 Test Results:")
    print(f"   Admin Login: {'✅ PASS' if admin_success else '❌ FAIL'}")
    print(f"   Regular User Check: {'✅ PASS' if regular_success else '❌ FAIL'}")
    print(f"   Test User Creation & Login: {'✅ PASS' if test_user_success else '❌ FAIL'}")
    
    if admin_success and regular_success and test_user_success:
        print(f"\n🎉 All login tests passed!")
        print(f"\n🌐 You can now test in the browser:")
        print(f"   Admin: <EMAIL> / admin123")
        print(f"   Test User: <EMAIL> / test123")
    else:
        print(f"\n⚠️ Some tests failed. Check the errors above.")
