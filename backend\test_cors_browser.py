#!/usr/bin/env python3
"""
Test CORS configuration by simulating browser requests
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_cors_preflight(url, method="GET", headers=None):
    """Test CORS preflight request (OPTIONS)"""
    print(f"\n🔍 Testing CORS Preflight for {method} {url}")
    
    # Simulate browser preflight request
    preflight_headers = {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': method,
        'Access-Control-Request-Headers': 'content-type,authorization'
    }
    
    try:
        response = requests.options(url, headers=preflight_headers)
        print(f"   OPTIONS Status: {response.status_code}")
        print(f"   Access-Control-Allow-Origin: {response.headers.get('Access-Control-Allow-Origin', 'NOT SET')}")
        print(f"   Access-Control-Allow-Methods: {response.headers.get('Access-Control-Allow-Methods', 'NOT SET')}")
        print(f"   Access-Control-Allow-Headers: {response.headers.get('Access-Control-Allow-Headers', 'NOT SET')}")
        print(f"   Access-Control-Allow-Credentials: {response.headers.get('Access-Control-Allow-Credentials', 'NOT SET')}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"   ❌ Preflight failed: {e}")
        return False

def test_actual_request(url, method="GET", data=None, headers=None):
    """Test actual request after preflight"""
    print(f"\n🚀 Testing Actual {method} Request to {url}")
    
    # Simulate browser request with Origin header
    request_headers = {
        'Origin': 'http://localhost:3000',
        'Content-Type': 'application/json'
    }
    if headers:
        request_headers.update(headers)
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=request_headers)
        elif method == 'POST':
            response = requests.post(url, json=data, headers=request_headers)
        
        print(f"   {method} Status: {response.status_code}")
        print(f"   Access-Control-Allow-Origin: {response.headers.get('Access-Control-Allow-Origin', 'NOT SET')}")
        print(f"   Content-Type: {response.headers.get('Content-Type', 'NOT SET')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if 'products' in data:
                    print(f"   ✅ Products returned: {len(data['products'])}")
                elif 'message' in data:
                    print(f"   ✅ Message: {data['message']}")
                elif 'access_token' in data:
                    print(f"   ✅ Login successful: {data['user']['email']}")
                else:
                    print(f"   ✅ Response: {str(data)[:100]}...")
            except:
                print(f"   ✅ Response received (non-JSON)")
        
        return response.status_code < 400
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
        return False

def main():
    print("🧪 CORS Browser Simulation Test")
    print("=" * 50)
    
    tests = [
        {
            'name': 'Root Endpoint',
            'url': f'{BASE_URL}/',
            'method': 'GET'
        },
        {
            'name': 'API Root',
            'url': f'{BASE_URL}/api',
            'method': 'GET'
        },
        {
            'name': 'Products API',
            'url': f'{BASE_URL}/api/products',
            'method': 'GET'
        },
        {
            'name': 'Health Check',
            'url': f'{BASE_URL}/api/health',
            'method': 'GET'
        },
        {
            'name': 'Categories API',
            'url': f'{BASE_URL}/api/categories',
            'method': 'GET'
        },
        {
            'name': 'Login API',
            'url': f'{BASE_URL}/api/auth/login',
            'method': 'POST',
            'data': {
                'email': '<EMAIL>',
                'password': 'admin123'
            }
        }
    ]
    
    results = []
    
    for test in tests:
        print(f"\n{'='*20} {test['name']} {'='*20}")
        
        # Test preflight
        preflight_ok = test_cors_preflight(test['url'], test['method'])
        
        # Test actual request
        actual_ok = test_actual_request(
            test['url'], 
            test['method'], 
            test.get('data'),
            test.get('headers')
        )
        
        results.append({
            'name': test['name'],
            'preflight': preflight_ok,
            'request': actual_ok,
            'overall': preflight_ok and actual_ok
        })
    
    # Summary
    print(f"\n{'='*50}")
    print("📋 CORS Test Results Summary")
    print(f"{'='*50}")
    
    all_passed = True
    for result in results:
        status = "✅ PASS" if result['overall'] else "❌ FAIL"
        print(f"   {result['name']:<20} {status}")
        if not result['overall']:
            all_passed = False
            if not result['preflight']:
                print(f"      └─ Preflight (OPTIONS) failed")
            if not result['request']:
                print(f"      └─ Actual request failed")
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 ALL CORS TESTS PASSED!")
        print("✅ Frontend integration should work perfectly")
        print("\n🚀 Ready for React/JavaScript fetch requests:")
        print("   fetch('http://127.0.0.1:5000/api/products')")
        print("   .then(response => response.json())")
        print("   .then(data => console.log(data.products));")
    else:
        print("⚠️  Some CORS tests failed")
        print("❌ Frontend may experience CORS errors")
        print("🔧 Check Flask CORS configuration")
    
    print(f"{'='*50}")

if __name__ == '__main__':
    main()
