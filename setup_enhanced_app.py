#!/usr/bin/env python3
"""
Setup script for Grace Medical Limited Enhanced Application
Helps set up and verify the enhanced application
"""

import os
import sys
import subprocess
import time

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step, description):
    """Print a formatted step"""
    print(f"\n{step}. {description}")
    print("-" * 40)

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")

def check_file_exists(filepath, description):
    """Check if a file exists"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ Missing {description}: {filepath}")
        return False

def install_python_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "flask", "flask-cors", "werkzeug"], 
                      check=True, capture_output=True)
        print("✅ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False

def check_node_and_npm():
    """Check if Node.js and npm are available"""
    try:
        node_version = subprocess.run(["node", "--version"], capture_output=True, text=True, check=True)
        npm_version = subprocess.run(["npm", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ Node.js {node_version.stdout.strip()} detected")
        print(f"✅ npm {npm_version.stdout.strip()} detected")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Node.js and npm are required for frontend development")
        print("   Please install Node.js from https://nodejs.org/")
        return False

def setup_backend():
    """Set up the backend"""
    print_step(1, "Setting up Enhanced Backend")
    
    # Check required files
    required_files = [
        ("backend/enhanced_app.py", "Enhanced Backend Application"),
        ("backend/database.py", "Database Models"),
        ("backend/config.py", "Configuration File")
    ]
    
    all_files_exist = True
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            all_files_exist = False
    
    if not all_files_exist:
        print("❌ Some backend files are missing. Please ensure all files are in place.")
        return False
    
    # Install dependencies
    if not install_python_dependencies():
        return False
    
    print("✅ Backend setup completed")
    return True

def setup_frontend():
    """Set up the frontend"""
    print_step(2, "Setting up Enhanced Frontend")
    
    # Check if Node.js is available
    if not check_node_and_npm():
        return False
    
    # Check required files
    required_files = [
        ("frontend/src/components/ProductCard.jsx", "Enhanced Product Card"),
        ("frontend/src/pages/EnhancedCart.jsx", "Enhanced Cart"),
        ("frontend/src/pages/EnhancedCheckout.jsx", "Enhanced Checkout")
    ]
    
    all_files_exist = True
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            all_files_exist = False
    
    if not all_files_exist:
        print("❌ Some frontend files are missing. Please ensure all files are in place.")
        return False
    
    # Check if package.json exists
    if os.path.exists("frontend/package.json"):
        print("✅ Frontend package.json found")
        
        # Install npm dependencies
        print("📦 Installing npm dependencies...")
        try:
            subprocess.run(["npm", "install"], cwd="frontend", check=True, capture_output=True)
            print("✅ npm dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install npm dependencies: {e}")
            return False
    else:
        print("❌ frontend/package.json not found")
        return False
    
    print("✅ Frontend setup completed")
    return True

def create_startup_scripts():
    """Create startup scripts"""
    print_step(3, "Creating Startup Scripts")
    
    # Backend startup script
    backend_script = """#!/bin/bash
echo "🚀 Starting Grace Medical Limited Enhanced Backend..."
cd backend
python enhanced_app.py
"""
    
    with open("start_backend.sh", "w") as f:
        f.write(backend_script)
    
    # Frontend startup script
    frontend_script = """#!/bin/bash
echo "🚀 Starting Grace Medical Limited Frontend..."
cd frontend
npm run dev
"""
    
    with open("start_frontend.sh", "w") as f:
        f.write(frontend_script)
    
    # Windows batch files
    backend_bat = """@echo off
echo 🚀 Starting Grace Medical Limited Enhanced Backend...
cd backend
python enhanced_app.py
pause
"""
    
    with open("start_backend.bat", "w") as f:
        f.write(backend_bat)
    
    frontend_bat = """@echo off
echo 🚀 Starting Grace Medical Limited Frontend...
cd frontend
npm run dev
pause
"""
    
    with open("start_frontend.bat", "w") as f:
        f.write(frontend_bat)
    
    print("✅ Startup scripts created:")
    print("   - start_backend.sh / start_backend.bat")
    print("   - start_frontend.sh / start_frontend.bat")

def run_tests():
    """Run the test suite"""
    print_step(4, "Running Test Suite")
    
    if not check_file_exists("test_enhanced_app.py", "Test Suite"):
        return False
    
    print("🧪 Test suite is ready to run")
    print("   To run tests: python test_enhanced_app.py")
    print("   (Make sure the backend is running first)")
    
    return True

def display_next_steps():
    """Display next steps"""
    print_step(5, "Next Steps")
    
    print("🎯 To start the application:")
    print("   1. Start the backend:")
    print("      python backend/enhanced_app.py")
    print("      OR")
    print("      ./start_backend.sh (Linux/Mac)")
    print("      start_backend.bat (Windows)")
    print()
    print("   2. Start the frontend (in a new terminal):")
    print("      cd frontend && npm run dev")
    print("      OR")
    print("      ./start_frontend.sh (Linux/Mac)")
    print("      start_frontend.bat (Windows)")
    print()
    print("   3. Open your browser:")
    print("      Backend API: http://localhost:5000")
    print("      Frontend: http://localhost:5173")
    print()
    print("   4. Run tests (optional):")
    print("      python test_enhanced_app.py")
    print()
    print("🔑 Default Admin Credentials:")
    print("   Email: <EMAIL>")
    print("   Password: admin123")
    print()
    print("🔐 API Key for Admin Routes:")
    print("   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")

def main():
    """Main setup function"""
    print_header("Grace Medical Limited - Enhanced Application Setup")
    
    # Check Python version
    check_python_version()
    
    # Setup backend
    if not setup_backend():
        print("\n❌ Backend setup failed. Please fix the issues and try again.")
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        print("\n❌ Frontend setup failed. Please fix the issues and try again.")
        sys.exit(1)
    
    # Create startup scripts
    create_startup_scripts()
    
    # Run tests
    run_tests()
    
    # Display next steps
    display_next_steps()
    
    print_header("Setup Completed Successfully! 🎉")
    
    print("\n📋 Summary of Enhancements:")
    print("✅ Fixed database schema issues (phone column in orders)")
    print("✅ Enhanced product image handling with placeholders")
    print("✅ Improved cart functionality without UI flickers")
    print("✅ Enhanced order placement with proper validation")
    print("✅ Modern, responsive UI/UX design")
    print("✅ Comprehensive error handling and logging")
    print("✅ Email notifications and stock alerts")
    print("✅ Role-based access control")
    print("✅ Complete test suite")
    
    print("\n🚀 Your enhanced Grace Medical Limited application is ready!")

if __name__ == "__main__":
    main()
