import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useCart } from '../contexts/CartContext'
import { Trash2, Plus, Minus, ShoppingBag, Package, AlertCircle } from 'lucide-react'
import toast from 'react-hot-toast'

const EnhancedCart = () => {
  const { cartItems, updateQuantity, removeFromCart, getCartTotal, clearCart } = useCart()
  const [isUpdating, setIsUpdating] = useState({})

  const getImageUrl = (item) => {
    if (item.image_url && item.image_url.trim() !== '') {
      return item.image_url
    }
    return `https://via.placeholder.com/100x100/e5e7eb/6b7280?text=${encodeURIComponent('Medical')}`
  }

  const handleImageError = (e) => {
    e.target.src = `https://via.placeholder.com/100x100/e5e7eb/6b7280?text=${encodeURIComponent('Medical')}`
  }

  const handleQuantityUpdate = async (itemId, newQuantity) => {
    if (newQuantity < 0) return
    
    setIsUpdating(prev => ({ ...prev, [itemId]: true }))
    
    try {
      if (newQuantity === 0) {
        await removeFromCart(itemId)
        toast.success('Item removed from cart')
      } else {
        await updateQuantity(itemId, newQuantity)
      }
    } catch (error) {
      toast.error('Failed to update cart')
    } finally {
      setIsUpdating(prev => ({ ...prev, [itemId]: false }))
    }
  }

  const handleRemoveItem = async (itemId, itemName) => {
    try {
      await removeFromCart(itemId)
      toast.success(`${itemName} removed from cart`)
    } catch (error) {
      toast.error('Failed to remove item')
    }
  }

  const handleClearCart = async () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      try {
        await clearCart()
        toast.success('Cart cleared')
      } catch (error) {
        toast.error('Failed to clear cart')
      }
    }
  }

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const calculateItemTotal = (item) => {
    // Use bulk price if quantity >= 10 and bulk price is available
    const price = item.bulk_price && item.quantity >= 10 ? item.bulk_price : item.unit_price
    return price * item.quantity
  }

  const calculateCartTotal = () => {
    return cartItems.reduce((total, item) => total + calculateItemTotal(item), 0)
  }

  const getCartCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0)
  }

  if (cartItems.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-16 bg-white rounded-xl shadow-lg">
          <ShoppingBag className="h-24 w-24 text-gray-400 mx-auto mb-6" />
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
          <p className="text-gray-600 mb-8 text-lg">Start shopping to add medical supplies to your cart</p>
          <Link
            to="/products"
            className="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-colors font-medium inline-flex items-center gap-2"
          >
            <Package className="h-5 w-5" />
            Browse Products
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900">Shopping Cart</h1>
        <button
          onClick={handleClearCart}
          className="text-red-600 hover:text-red-700 font-medium flex items-center gap-2"
        >
          <Trash2 className="h-4 w-4" />
          Clear Cart
        </button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            {cartItems.map((item, index) => {
              const itemTotal = calculateItemTotal(item)
              const isUsingBulkPrice = item.bulk_price && item.quantity >= 10
              
              return (
                <div key={item.id} className={`p-6 ${index !== cartItems.length - 1 ? 'border-b border-gray-200' : ''}`}>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <img
                        src={getImageUrl(item)}
                        alt={item.name}
                        className="w-24 h-24 object-cover rounded-lg"
                        onError={handleImageError}
                      />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-bold text-gray-900 mb-1">{item.name}</h3>
                      <p className="text-gray-600 text-sm mb-2 line-clamp-2">{item.description}</p>
                      
                      <div className="flex items-center gap-4 mb-3">
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          {item.category}
                        </span>
                        {item.manufacturer && (
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                            {item.manufacturer}
                          </span>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-baseline gap-2">
                            <span className="text-lg font-bold text-primary-600">
                              {formatPrice(isUsingBulkPrice ? item.bulk_price : item.unit_price)}
                            </span>
                            <span className="text-sm text-gray-500">per unit</span>
                          </div>
                          {isUsingBulkPrice && (
                            <div className="flex items-center gap-1 mt-1">
                              <AlertCircle className="h-3 w-3 text-green-600" />
                              <span className="text-xs text-green-600 font-medium">Bulk pricing applied</span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleQuantityUpdate(item.id, item.quantity - 1)}
                            disabled={isUpdating[item.id]}
                            className="p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
                          >
                            <Minus className="h-4 w-4" />
                          </button>
                          
                          <div className="w-16 text-center">
                            {isUpdating[item.id] ? (
                              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mx-auto"></div>
                            ) : (
                              <span className="font-bold text-lg">{item.quantity}</span>
                            )}
                          </div>
                          
                          <button
                            onClick={() => handleQuantityUpdate(item.id, item.quantity + 1)}
                            disabled={isUpdating[item.id]}
                            className="p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
                          >
                            <Plus className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right flex-shrink-0">
                      <p className="text-xl font-bold text-gray-900 mb-2">
                        {formatPrice(itemTotal)}
                      </p>
                      <button
                        onClick={() => handleRemoveItem(item.id, item.name)}
                        className="text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-50 transition-colors"
                        title="Remove item"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        
        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-lg p-6 sticky top-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h2>
            
            <div className="space-y-4 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Items ({getCartCount()})</span>
                <span className="font-semibold">{formatPrice(calculateCartTotal())}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Shipping</span>
                <span className="font-semibold text-green-600">Free</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Tax</span>
                <span className="font-semibold">Calculated at checkout</span>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold">Total</span>
                  <span className="text-2xl font-bold text-primary-600">
                    {formatPrice(calculateCartTotal())}
                  </span>
                </div>
              </div>
            </div>

            {/* Bulk pricing info */}
            {cartItems.some(item => item.bulk_price) && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-green-800 mb-1">Bulk Pricing Available</h4>
                    <p className="text-sm text-green-700">
                      Order 10+ units of eligible items to get bulk pricing automatically applied.
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            <Link
              to="/checkout"
              className="w-full bg-primary-600 text-white py-4 px-6 rounded-lg hover:bg-primary-700 transition-colors font-medium text-center block mb-4 text-lg"
            >
              Proceed to Checkout
            </Link>
            
            <Link
              to="/products"
              className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium text-center block"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EnhancedCart
