@echo off
echo Starting Grace Medical Limited Frontend (Demo Mode)...
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 16+ from https://nodejs.org/downloads/
    pause
    exit /b 1
)

echo.
echo Setting up frontend...
cd frontend

echo Installing frontend dependencies...
if not exist node_modules (
    echo Installing Node.js dependencies...
    npm install
)

echo.
echo Starting frontend development server...
echo.
echo ========================================
echo Grace Medical Limited (Demo Mode)
echo ========================================
echo.
echo The application will run with demo data since
echo the backend dependencies are not installed.
echo.
echo Demo Login Credentials:
echo.
echo Doctor Account:
echo Email: <EMAIL>
echo Password: password123
echo.
echo Admin Account:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo Frontend will be available at: http://localhost:3000
echo.
echo ========================================

npm run dev
