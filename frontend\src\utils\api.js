import axios from 'axios'
import Cookies from 'js-cookie'
import { mockAPI } from './mockData'

// Check if backend is available
const USE_MOCK_DATA = true; // Set to false when backend is running

// Create axios instance
export const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      Cookies.remove('access_token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Helper function to handle API calls with fallback to mock data
const apiCall = async (apiFunction, mockFunction, ...args) => {
  if (USE_MOCK_DATA) {
    try {
      const result = await mockFunction(...args)
      if (result.success) {
        return { data: result.data }
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      throw { response: { data: { error: error.message } } }
    }
  } else {
    return await apiFunction(...args)
  }
}

// Admin API instance with API key
export const adminApi = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': '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',
  },
})

// API functions with mock data fallback
export const authAPI = {
  login: (credentials) => apiCall(
    () => api.post('/auth/login', credentials),
    () => mockAPI.login(credentials.email, credentials.password)
  ),
  register: (userData) => apiCall(
    () => api.post('/auth/register', userData),
    () => mockAPI.register(userData)
  ),
  verifyEmail: (token) => apiCall(
    () => api.post('/auth/verify-email', { token }),
    () => Promise.resolve({ success: true, message: 'Email verified successfully' })
  ),
}

export const productsAPI = {
  getProducts: (params) => apiCall(
    () => api.get('/products', { params }),
    () => mockAPI.getProducts(params)
  ),
  getProduct: (id) => apiCall(
    () => api.get(`/products/${id}`),
    () => mockAPI.getProduct(id)
  ),
  getCategories: () => apiCall(
    () => api.get('/categories'),
    () => mockAPI.getCategories()
  ),
  createProduct: (data) => adminApi.post('/products', data),
  updateProduct: (id, data) => adminApi.put(`/products/${id}`, data),
}

export const ordersAPI = {
  getUserOrders: (params) => apiCall(
    () => api.get('/orders', { params }),
    () => mockAPI.getUserOrders(params)
  ),
  createOrder: (orderData) => apiCall(
    () => api.post('/orders', orderData),
    () => mockAPI.createOrder(orderData)
  ),
  getOrder: (id) => apiCall(
    () => api.get(`/orders/${id}`),
    () => mockAPI.getOrder(id)
  ),
  getAllOrders: (params) => adminApi.get('/admin/orders', { params }),
  updateOrderStatus: (id, status) => adminApi.put(`/admin/orders/${id}/status`, { status }),
}

export const adminAPI = {
  getLowStockProducts: () => adminApi.get('/admin/low-stock'),
  getNearExpiryProducts: (days) => adminApi.get('/admin/near-expiry', { params: { days } }),
  getReports: (params) => adminApi.get('/admin/reports', { params }),
}

export const notificationsAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  markAsRead: (id) => api.put(`/notifications/${id}/read`),
}
