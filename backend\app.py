
from flask import Flask, jsonify, request, send_from_directory, session
from flask_cors import CORS
from datetime import datetime, timedelta
from functools import wraps
from werkzeug.security import generate_password_hash, check_password_hash
import hashlib
import os
import sqlite3
import csv
import uuid
import json
import time
from werkzeug.utils import secure_filename

# Try to import JWT libraries, but make them optional
try:
    from flask_jwt_extended import (
        JWTManager, create_access_token, get_jwt_identity, 
        verify_jwt_in_request
    )
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

# Initialize Flask app
app = Flask(__name__)

# Configure CORS for frontend integration
try:
    CORS(app,
         origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:5173", "http://127.0.0.1:5173", "*"],
         allow_headers=["Content-Type", "Authorization", "X-API-Key", "Access-Control-Allow-Credentials"],
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         supports_credentials=True)
    CORS_AVAILABLE = True
    print("✅ CORS enabled for frontend integration")
except Exception as e:
    CORS_AVAILABLE = False
    print(f"⚠️ CORS setup failed: {e}")

# Only add manual CORS headers if flask-cors is not available
if not CORS_AVAILABLE:
    @app.after_request
    def after_request(response):
        """Add CORS headers to all responses"""
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-API-Key')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        response.headers.add('Access-Control-Allow-Credentials', 'true')
        return response

    # Handle preflight OPTIONS requests
    @app.before_request
    def handle_preflight():
        """Handle CORS preflight requests"""
        if request.method == "OPTIONS":
            response = jsonify({'status': 'OK'})
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add('Access-Control-Allow-Headers', "Content-Type,Authorization,X-API-Key")
            response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
            response.headers.add('Access-Control-Allow-Credentials', 'true')
            return response

# Configure JWT if available
if JWT_AVAILABLE:
    app.config['JWT_SECRET_KEY'] = 'your-secret-key'  # Change this in production!
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    jwt = JWTManager(app)

# API Key for admin routes
API_KEY = "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"

# Database file path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def init_database():
    """Initialize the database with tables"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            phone TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            is_verified BOOLEAN DEFAULT TRUE,
            verification_token TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create products table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            packaging TEXT,
            unit_price REAL NOT NULL,
            bulk_price REAL,
            manufacturer TEXT,
            expiry_date DATE,
            stock_quantity INTEGER DEFAULT 0,
            reorder_level INTEGER DEFAULT 10,
            category TEXT,
            image_url TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create orders table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            order_number TEXT UNIQUE NOT NULL,
            status TEXT DEFAULT 'Pending',
            total_amount REAL NOT NULL,
            shipping_address TEXT,
            billing_address TEXT,
            phone TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Create order_items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')

    # Create notifications table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type TEXT DEFAULT 'info',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    conn.commit()
    conn.close()

def migrate_database():
    """Apply database migrations for existing databases"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # Check if phone column exists in orders table
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        if 'phone' not in column_names:
            print("🔧 Adding phone column to orders table...")
            cursor.execute('ALTER TABLE orders ADD COLUMN phone TEXT')
            print("✅ Phone column added successfully")

        # Check if email column exists in orders table
        if 'email' not in column_names:
            print("🔧 Adding email column to orders table...")
            cursor.execute('ALTER TABLE orders ADD COLUMN email TEXT')
            print("✅ Email column added successfully")

        # Fix product images with placeholder URLs
        # Get ALL products to check their image URLs
        cursor.execute('SELECT id, name, image_url FROM products')
        all_products = cursor.fetchall()

        products_to_fix = []
        for product_id, product_name, image_url in all_products:
            # Fix products with missing, null, empty, or local file path images
            needs_fix = (
                image_url is None or
                image_url == '' or
                image_url == 'null' or
                (image_url and image_url.startswith('/images/')) or
                (image_url and not image_url.startswith('http'))
            )
            if needs_fix:
                products_to_fix.append((product_id, product_name, image_url))

        if products_to_fix:
            print(f"🖼️ Fixing {len(products_to_fix)} products with missing/invalid images...")

            for product_id, product_name, old_url in products_to_fix:
                # Create a placeholder image URL using Picsum photos
                placeholder_url = f"https://picsum.photos/300/200?random={product_id}"

                cursor.execute('''
                    UPDATE products
                    SET image_url = ?
                    WHERE id = ?
                ''', (placeholder_url, product_id))

                print(f"  ✅ Fixed product {product_id}: {product_name}")
                print(f"     Old: {old_url}")
                print(f"     New: {placeholder_url}")

            print("✅ Product images fixed with placeholders")

        conn.commit()
        print("✅ Database migration completed successfully")

    except Exception as e:
        print(f"❌ Migration error: {e}")
        conn.rollback()
    finally:
        conn.close()

def import_products_from_csv():
    """Import products from CSV file"""
    csv_path = os.path.join(os.path.dirname(__file__), 'data', 'surgical_supply_catalog_100items.csv')

    if not os.path.exists(csv_path):
        print(f"CSV file not found at {csv_path}")
        return False

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check if products already exist - ONLY import on first run
    cursor.execute('SELECT COUNT(*) FROM products')
    existing_count = cursor.fetchone()[0]
    if existing_count > 0:
        print(f"✅ Database already contains {existing_count} products. Skipping CSV import.")
        conn.close()
        return True

    print("🔄 First run detected - importing products from CSV...")

    try:
        with open(csv_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            products_added = 0

            for row in csv_reader:
                # Parse expiry date
                expiry_date = None
                if row['expiry_date']:
                    try:
                        expiry_date = datetime.strptime(row['expiry_date'], '%Y-%m-%d').date()
                    except:
                        expiry_date = None

                # Handle image URL properly - store NULL if empty, let frontend handle placeholder
                image_url = row.get('image_url', '').strip()
                if not image_url or image_url.lower() in ['', 'null', 'none', 'n/a']:
                    image_url = None

                cursor.execute('''
                    INSERT INTO products (
                        name, description, packaging, unit_price, bulk_price,
                        manufacturer, expiry_date, stock_quantity, reorder_level,
                        category, image_url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row['name'],
                    row['description'],
                    row['packaging'],
                    float(row['unit_price']),
                    float(row['bulk_price']) if row['bulk_price'] else None,
                    row['manufacturer'],
                    expiry_date,
                    int(row['stock_quantity']),
                    int(row['reorder_level']),
                    row.get('category', 'General'),
                    image_url
                ))
                products_added += 1

        conn.commit()
        print(f"Successfully imported {products_added} products from CSV")
        return True

    except Exception as e:
        print(f"Error importing products from CSV: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

def create_admin_user():
    """Create default admin user if none exists"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute('SELECT COUNT(*) FROM users WHERE is_admin = 1')
    if cursor.fetchone()[0] == 0:
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, is_admin, is_verified)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            '<EMAIL>',
            generate_password_hash('admin123'),
            'Admin',
            'User',
            True,
            True
        ))
        conn.commit()
        print("Default admin user created: <EMAIL> / admin123")

    conn.close()

def generate_invoice_pdf(order_data):
    """Generate a simple PDF invoice"""
    try:
        # Create a simple HTML invoice
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Invoice #{order_data['order_number']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .company-name {{ font-size: 24px; font-weight: bold; color: #0284c7; }}
                .invoice-title {{ font-size: 20px; margin: 20px 0; }}
                .order-info {{ margin: 20px 0; }}
                .items-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .items-table th, .items-table td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                .items-table th {{ background-color: #f8f9fa; }}
                .total-row {{ font-weight: bold; background-color: #f0f9ff; }}
                .footer {{ margin-top: 30px; text-align: center; color: #666; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">Grace Medical Limited</div>
                <p>Your trusted partner for quality medical supplies</p>
            </div>

            <div class="invoice-title">INVOICE #{order_data['order_number']}</div>

            <div class="order-info">
                <p><strong>Order Date:</strong> {order_data.get('created_at', 'N/A')}</p>
                <p><strong>Status:</strong> {order_data.get('status', 'Pending')}</p>
                <p><strong>Shipping Address:</strong><br>{order_data.get('shipping_address', 'N/A')}</p>
                <p><strong>Phone:</strong> {order_data.get('phone', 'N/A')}</p>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Add items to the invoice
        for item in order_data.get('items', []):
            html_content += f"""
                    <tr>
                        <td>{item.get('product_name', 'Unknown Product')}</td>
                        <td>{item.get('quantity', 0)}</td>
                        <td>${item.get('unit_price', 0):.2f}</td>
                        <td>${item.get('total_price', 0):.2f}</td>
                    </tr>
            """

        html_content += f"""
                    <tr class="total-row">
                        <td colspan="3">Total Amount</td>
                        <td>${order_data.get('total_amount', 0):.2f}</td>
                    </tr>
                </tbody>
            </table>

            <div class="footer">
                <p>Thank you for your business!</p>
                <p>Grace Medical Limited | Email: <EMAIL> | Phone: ******-567-8900</p>
            </div>
        </body>
        </html>
        """

        # For now, just return the HTML content
        # In a full implementation, you would use a library like weasyprint or reportlab to generate actual PDF
        return html_content

    except Exception as e:
        print(f"Error generating invoice PDF: {str(e)}")
        return None

def send_order_notification_email(order_data):
    """Send comprehensive email notification for new order"""
    try:
        print(f"📧 NEW ORDER EMAIL NOTIFICATION SENT:")
        print(f"To: <EMAIL>")
        print(f"Subject: 🛒 New Order Received - #{order_data['order_number']}")
        print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"")
        print(f"📋 ORDER DETAILS:")
        print(f"Order Number: {order_data['order_number']}")
        print(f"Order Total: ${order_data['total_amount']}")
        print(f"Status: {order_data['status']}")
        print(f"")
        print(f"👤 CUSTOMER INFORMATION:")
        print(f"Phone: {order_data.get('phone', 'N/A')}")
        print(f"Shipping Address: {order_data.get('shipping_address', 'N/A')}")
        if order_data.get('notes'):
            print(f"Notes: {order_data['notes']}")
        print(f"")
        print(f"📦 ITEMS ORDERED:")
        for item in order_data.get('items', []):
            print(f"- {item['quantity']}x {item['product_name']} @ ${item['unit_price']} = ${item['total_price']}")
        print(f"")
        print(f"🔗 Actions Required:")
        print(f"- Review order in admin dashboard")
        print(f"- Confirm stock availability")
        print(f"- Update order status to 'Confirmed'")
        print(f"- Prepare items for dispatch")
        print("=" * 60)
        return True
    except Exception as e:
        print(f"❌ Error sending order notification email: {str(e)}")
        return False

def check_low_stock_and_expiry():
    """Check for low stock and near-expiry items (daily cron job simulation)"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check low stock items
        cursor.execute('''
            SELECT name, stock_quantity, reorder_level
            FROM products
            WHERE stock_quantity <= reorder_level AND is_active = 1
        ''')
        low_stock_items = cursor.fetchall()

        # Check near-expiry items (within 30 days)
        cursor.execute('''
            SELECT name, expiry_date
            FROM products
            WHERE expiry_date IS NOT NULL
            AND date(expiry_date) <= date('now', '+30 days')
            AND is_active = 1
        ''')
        near_expiry_items = cursor.fetchall()

        conn.close()

        # Send email alerts if items found
        if low_stock_items or near_expiry_items:
            send_daily_stock_alert_email(low_stock_items, near_expiry_items)

        # Print alerts to console
        if low_stock_items:
            print("🚨 LOW STOCK ALERT:")
            for item in low_stock_items:
                print(f"- {item[0]}: {item[1]} units (reorder level: {item[2]})")

        if near_expiry_items:
            print("⚠️ NEAR EXPIRY ALERT:")
            for item in near_expiry_items:
                print(f"- {item[0]}: expires on {item[1]}")

        if not low_stock_items and not near_expiry_items:
            print("✅ All products are well stocked and not near expiry")

        return {
            'low_stock_items': low_stock_items,
            'near_expiry_items': near_expiry_items
        }

    except Exception as e:
        print(f"Error checking stock and expiry: {str(e)}")
        return None

def send_daily_stock_alert_email(low_stock_items, near_expiry_items):
    """Send daily stock and expiry alert email to admin"""
    try:
        print(f"📧 DAILY STOCK ALERT EMAIL SENT:")
        print(f"To: <EMAIL>")
        print(f"Subject: Daily Stock & Expiry Alert - Grace Medical Limited")
        print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if low_stock_items:
            print(f"\n🚨 LOW STOCK ITEMS ({len(low_stock_items)}):")
            for item in low_stock_items:
                print(f"- {item[0]}: {item[1]} units (reorder at: {item[2]})")

        if near_expiry_items:
            print(f"\n⚠️ NEAR EXPIRY ITEMS ({len(near_expiry_items)}):")
            for item in near_expiry_items:
                print(f"- {item[0]}: expires on {item[1]}")

        print("=" * 60)
        return True
    except Exception as e:
        print(f"Error sending daily alert email: {str(e)}")
        return False

def run_daily_cron_jobs():
    """Simulate daily cron jobs - in production, use actual cron or task scheduler"""
    print(f"\n🕐 Running daily cron jobs at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    check_low_stock_and_expiry()
    print("✅ Daily cron jobs completed\n")

# Mock data for products (will be replaced by database)
MOCK_PRODUCTS = [
    {
        "id": 2,
        "name": "Nitrile Examination Gloves",
        "description": "Powder-free nitrile gloves for medical examination",
        "packaging": "Box of 100",
        "unit_price": 18.50,
        "bulk_price": 16.75,
        "manufacturer": "SafeGuard Medical",
        "expiry_date": "2025-06-30",
        "stock_quantity": 800,
        "reorder_level": 100,
        "category": "Gloves",
        "image_url": "/images/nitrile-gloves.jpg",
        "is_active": True,
        "is_low_stock": False,
        "is_near_expiry": False
    },
    {
        "id": 3,
        "name": "Surgical Face Masks",
        "description": "3-layer disposable face masks with ear loops",
        "packaging": "Box of 50",
        "unit_price": 12.99,
        "bulk_price": 11.50,
        "manufacturer": "ProtectMed",
        "expiry_date": "2025-09-15",
        "stock_quantity": 1200,
        "reorder_level": 150,
        "category": "PPE",
        "image_url": "/images/face-masks.jpg",
        "is_active": True,
        "is_low_stock": False,
        "is_near_expiry": False
    },
    {
        "id": 4,
        "name": "Digital Thermometer",
        "description": "Non-contact infrared thermometer with LCD display",
        "packaging": "Individual unit",
        "unit_price": 45.00,
        "bulk_price": 42.00,
        "manufacturer": "TempCheck Pro",
        "expiry_date": "2026-03-20",
        "stock_quantity": 75,
        "reorder_level": 10,
        "category": "Instruments",
        "image_url": "/images/thermometer.jpg",
        "is_active": True,
        "is_low_stock": False,
        "is_near_expiry": False
    },
    {
        "id": 5,
        "name": "Antiseptic Solution 500ml",
        "description": "Povidone iodine antiseptic solution for wound care",
        "packaging": "Bottle 500ml",
        "unit_price": 8.75,
        "bulk_price": 7.99,
        "manufacturer": "CleanCare",
        "expiry_date": "2025-08-10",
        "stock_quantity": 200,
        "reorder_level": 25,
        "category": "Antiseptics",
        "image_url": "/images/antiseptic.jpg",
        "is_active": True,
        "is_low_stock": False,
        "is_near_expiry": False
    }
]

# Mock users storage (in production, this would be in a database)
MOCK_USERS = [
    {
        "id": 1,
        "email": "<EMAIL>",
        "password_hash": hashlib.sha256("admin123".encode()).hexdigest(),
        "first_name": "Admin",
        "last_name": "User",
        "phone": "",
        "is_admin": True,
        "is_verified": True,
        "created_at": datetime.now().isoformat()
    }
]

# Mock orders storage
MOCK_ORDERS = []

# Configure upload folder
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Create uploads directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Helper functions for mock data
def find_user_by_email(email):
    """Find user by email in mock data"""
    for user in MOCK_USERS:
        if user['email'] == email:
            return user
    return None

def verify_password(password, password_hash):
    """Verify password against hash"""
    return hashlib.sha256(password.encode()).hexdigest() == password_hash

def get_products_with_filters(search=None, category=None, low_stock=None, near_expiry=None):
    """Get products with optional filters"""
    products = MOCK_PRODUCTS.copy()

    if search:
        search_lower = search.lower()
        products = [p for p in products if
                   search_lower in p['name'].lower() or
                   search_lower in p['description'].lower() or
                   search_lower in p['manufacturer'].lower()]

    if category:
        products = [p for p in products if p['category'].lower() == category.lower()]

    if low_stock:
        products = [p for p in products if p['stock_quantity'] <= p['reorder_level']]

    if near_expiry:
        # Mock near expiry logic - products expiring within 30 days
        thirty_days_from_now = (datetime.now() + timedelta(days=30)).date()
        products = [p for p in products if
                   datetime.strptime(p['expiry_date'], '%Y-%m-%d').date() <= thirty_days_from_now]

    return products

def require_api_key(f):
    """Decorator to require API key for admin routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key != API_KEY:
            return jsonify({'error': 'Invalid or missing API key'}), 401
        return f(*args, **kwargs)
    return decorated_function

def require_auth(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # For development/testing, always allow admin access
        # This makes the system more permissive during development

        if not JWT_AVAILABLE:
            # If JWT is not available, use the first admin user for testing
            current_user = MOCK_USERS[0]  # Default admin user
            return f(current_user, *args, **kwargs)

        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
            # Find user in mock data
            current_user = next((u for u in MOCK_USERS if u['id'] == current_user_id), None)
            if not current_user:
                return jsonify({'error': 'User not found'}), 404
            return f(current_user, *args, **kwargs)
        except Exception as e:
            # For development, be more permissive - log the error but allow access
            print(f"Auth warning: {str(e)} - Using default admin user for development")
            current_user = MOCK_USERS[0]  # Default admin user
            return f(current_user, *args, **kwargs)

            # In production, uncomment this line instead:
            # return jsonify({'error': 'Invalid or missing token'}), 401
    return decorated_function

# Home Route - serve a simple web interface
@app.route('/')
def home():
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grace Medical Limited</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">GM</span>
                            </div>
                            <span class="ml-2 text-xl font-bold text-gray-800">Grace Medical</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="showCart()" class="relative text-primary-600 hover:text-primary-700 font-medium">
                            🛒 Cart
                            <span id="cart-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </button>
                        <div id="auth-buttons" class="flex items-center space-x-4">
                            <button onclick="showLogin()" class="text-primary-600 hover:text-primary-700 font-medium">Login</button>
                            <button onclick="showRegister()" class="text-primary-600 hover:text-primary-700 font-medium">Register</button>
                        </div>
                        <div id="user-menu" class="hidden flex items-center space-x-4">
                            <button onclick="showOrders()" class="text-primary-600 hover:text-primary-700 font-medium">My Orders</button>
                            <button onclick="showNotifications()" class="text-primary-600 hover:text-primary-700 font-medium relative">
                                Notifications
                                <span id="notification-badge" class="hidden absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                            </button>
                            <button id="admin-button" onclick="showAdmin()" class="hidden bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded font-medium">Admin</button>

                            <!-- User Info Display -->
                            <div id="user-info-display" class="flex items-center space-x-3 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                                <div class="flex flex-col text-sm">
                                    <span id="user-display-name" class="font-semibold text-blue-900">Dr. User</span>
                                    <span id="user-display-role" class="text-blue-600 text-xs">Doctor</span>
                                </div>
                            </div>

                            <!-- Enhanced Profile Section -->
                            <div class="relative">
                                <button id="profile-button" onclick="toggleProfileMenu()" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors">
                                    <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                        <span id="user-initials">U</span>
                                    </div>
                                    <span id="user-name" class="font-medium"></span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                <!-- Profile Dropdown Menu -->
                                <div id="profile-dropdown" class="hidden absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border-2 border-primary-200 z-[9999]" style="z-index: 9999 !important; box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;">
                                    <div class="p-4 border-b border-gray-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center text-lg font-medium">
                                                <span id="user-initials-large">U</span>
                                            </div>
                                            <div>
                                                <div id="user-full-name" class="font-semibold text-gray-900"></div>
                                                <div id="user-email" class="text-sm text-gray-600"></div>
                                                <div id="user-role" class="text-xs text-primary-600 font-medium"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-2">
                                        <button onclick="showProfile(); toggleProfileMenu();" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md flex items-center space-x-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            <span>View Profile</span>
                                        </button>
                                        <button onclick="logout(); toggleProfileMenu();" class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md flex items-center space-x-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                            </svg>
                                            <span>Logout</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="showProducts()" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium">View Products</button>
                            <button onclick="showCart()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium relative">
                                🛒 Cart
                                <span id="cart-badge" class="hidden absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div id="main-content">
            <!-- Hero Section -->
            <div class="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
                <div class="max-w-7xl mx-auto px-4 py-16">
                    <div class="text-center">
                        <h1 class="text-4xl md:text-6xl font-bold mb-4">Grace Medical Limited</h1>
                        <p class="text-xl md:text-2xl mb-8 opacity-90">Your trusted partner for quality medical supplies</p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button onclick="showProducts()" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                Browse Products
                            </button>
                            <button onclick="showLogin()" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                                Login
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="py-16 bg-white">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">Why Choose Grace Medical?</h2>
                        <p class="text-gray-600 max-w-2xl mx-auto">We provide high-quality medical supplies with reliable service and competitive pricing.</p>
                    </div>
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-primary-600 text-2xl">🏥</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Quality Products</h3>
                            <p class="text-gray-600">Premium medical supplies from trusted manufacturers</p>
                        </div>
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-primary-600 text-2xl">🚚</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Fast Delivery</h3>
                            <p class="text-gray-600">Quick and reliable shipping to your location</p>
                        </div>
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-primary-600 text-2xl">💰</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Competitive Prices</h3>
                            <p class="text-gray-600">Best prices with bulk discounts available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Section -->
        <div id="profile-section" class="hidden">
            <div class="max-w-4xl mx-auto px-4 py-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">My Profile</h2>
                    <button onclick="showHome()" class="text-primary-600 hover:text-primary-700 font-medium">← Back to Home</button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <!-- Profile Header -->
                    <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-8">
                        <div class="flex items-center space-x-6">
                            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white text-3xl font-bold">
                                <span id="profile-initials">U</span>
                            </div>
                            <div class="text-white">
                                <h3 id="profile-full-name" class="text-2xl font-bold mb-2"></h3>
                                <p id="profile-email" class="text-primary-100 mb-1"></p>
                                <span id="profile-role" class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm font-medium"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Content -->
                    <div class="p-6">
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- Account Information -->
                            <div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-4">Account Information</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                                        <div id="profile-display-name" class="text-gray-900 font-medium"></div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Email Address</label>
                                        <div id="profile-display-email" class="text-gray-900"></div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Account Type</label>
                                        <div id="profile-display-role" class="text-gray-900"></div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">Member Since</label>
                                        <div id="profile-member-since" class="text-gray-900"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Statistics -->
                            <div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-4">Account Statistics</h4>
                                <div class="space-y-4">
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <span class="text-gray-600">Total Orders</span>
                                            <span id="profile-total-orders" class="text-2xl font-bold text-primary-600">0</span>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <span class="text-gray-600">Total Spent</span>
                                            <span id="profile-total-spent" class="text-2xl font-bold text-green-600">$0.00</span>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <span class="text-gray-600">Last Order</span>
                                            <span id="profile-last-order" class="text-sm text-gray-600">Never</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h4>
                            <div class="flex flex-wrap gap-4">
                                <button onclick="showOrders()" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                    View My Orders
                                </button>
                                <button onclick="showProducts()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                    Browse Products
                                </button>
                                <button onclick="logout()" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors" style="display: block !important; visibility: visible !important;">
                                    🚪 Logout
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Modal -->
        <div id="login-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Login</h2>
                    <button onclick="hideLogin()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                <form id="login-form" onsubmit="handleLogin(event)">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                        <input type="email" id="login-email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" id="login-password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-lg">
                        Login
                    </button>
                </form>
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">Demo Admin: <EMAIL> / admin123</p>
                    <p class="text-sm text-gray-600 mt-2">
                        Don't have an account?
                        <button onclick="hideLogin(); showRegister();" class="text-primary-600 hover:text-primary-700 font-medium">Register here</button>
                    </p>
                </div>
            </div>
        </div>

        <!-- Registration Modal -->
        <div id="register-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Register</h2>
                    <button onclick="hideRegister()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                <form id="register-form" onsubmit="handleRegister(event)">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">First Name</label>
                            <input type="text" id="register-first-name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">Last Name</label>
                            <input type="text" id="register-last-name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                        <input type="email" id="register-email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Phone</label>
                        <input type="tel" id="register-phone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" id="register-password" required minlength="6" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Minimum 6 characters</p>
                    </div>
                    <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-lg">
                        Register
                    </button>
                </form>
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account?
                        <button onclick="hideRegister(); showLogin();" class="text-primary-600 hover:text-primary-700 font-medium">Login here</button>
                    </p>
                </div>
            </div>
        </div>

        <!-- Cart Modal -->
        <div id="cart-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Shopping Cart</h2>
                    <button onclick="hideCart()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>

                <div id="cart-items" class="space-y-4 mb-6">
                    <!-- Cart items will be populated here -->
                </div>

                <div id="cart-empty" class="text-center py-8 text-gray-500 hidden">
                    <div class="text-6xl mb-4">🛒</div>
                    <p class="text-lg">Your cart is empty</p>
                    <button onclick="hideCart(); showProducts();" class="mt-4 bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg">
                        Continue Shopping
                    </button>
                </div>

                <div id="cart-summary" class="border-t pt-4 hidden">
                    <div class="flex justify-between items-center mb-4">
                        <span class="text-lg font-semibold">Total:</span>
                        <span id="cart-total" class="text-2xl font-bold text-primary-600">$0.00</span>
                    </div>
                    <div class="flex gap-4">
                        <button onclick="clearCart()" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            Clear Cart
                        </button>
                        <button onclick="proceedToCheckout()" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg">
                            Proceed to Checkout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkout Modal -->
        <div id="checkout-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Checkout</h2>
                    <button onclick="hideCheckout()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Order Summary -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Order Summary</h3>
                        <div id="checkout-items" class="space-y-3 mb-4 max-h-60 overflow-y-auto">
                            <!-- Order items will be populated here -->
                        </div>
                        <div class="border-t pt-4">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold">Total:</span>
                                <span id="checkout-total" class="text-xl font-bold text-primary-600">$0.00</span>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Form -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Shipping Information</h3>
                        <form id="checkout-form" onsubmit="handleCheckout(event)">
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                        <input type="text" id="checkout-first-name" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                        <input type="text" id="checkout-last-name" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="checkout-email" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input type="tel" id="checkout-phone" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Shipping Address</label>
                                    <textarea id="checkout-address" required rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                              placeholder="Enter your complete shipping address"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Order Notes (Optional)</label>
                                    <textarea id="checkout-notes" rows="2"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                              placeholder="Any special instructions or notes"></textarea>
                                </div>

                                <div class="flex gap-4 pt-4">
                                    <button type="button" onclick="hideCheckout(); showCart();"
                                            class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                        Back to Cart
                                    </button>
                                    <button type="submit"
                                            class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg">
                                        Place Order
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Section - Professional Medical Supply Catalog -->
        <div id="products-section" class="hidden">
            <!-- Header Section -->
            <div class="bg-white border-b">
                <div class="max-w-7xl mx-auto px-4 py-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Medical Products Catalog</h1>
                            <p class="mt-2 text-gray-600">Professional medical supplies and equipment</p>
                        </div>
                        <div class="mt-4 lg:mt-0 flex items-center space-x-4">
                            <div class="relative">
                                <input type="text" id="search-input" placeholder="Search products..."
                                       class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <select id="sort-select" class="border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="name">Sort by Name</option>
                                <option value="price-low">Price: Low to High</option>
                                <option value="price-high">Price: High to Low</option>
                                <option value="stock">Stock Level</option>
                                <option value="featured">Featured First</option>
                            </select>
                            <button onclick="showHome()" class="text-primary-600 hover:text-primary-700 font-medium">← Back to Home</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-7xl mx-auto px-4 py-6">
                <div class="flex flex-col lg:flex-row gap-6">
                    <!-- Category Sidebar -->
                    <div class="lg:w-64 flex-shrink-0">
                        <div class="bg-white rounded-lg shadow-sm border p-6 sticky top-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Categories</h3>

                            <!-- All Products -->
                            <div class="space-y-2">
                                <button onclick="filterByCategory('')"
                                        class="category-filter w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors active"
                                        data-category="">
                                    <div class="flex items-center justify-between">
                                        <span class="font-medium text-primary-600">All Products</span>
                                        <span id="count-all" class="text-sm text-gray-500">0</span>
                                    </div>
                                </button>
                            </div>

                            <div class="border-t my-4"></div>

                            <!-- Category List -->
                            <div id="category-list" class="space-y-2">
                                <!-- Categories will be populated here -->
                            </div>

                            <!-- Price Range Filter -->
                            <div class="border-t my-4"></div>
                            <h4 class="font-semibold text-gray-900 mb-3">Price Range</h4>
                            <div class="space-y-2">
                                <button onclick="filterByPrice(0, 10)" class="price-filter w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                                    Under $10
                                </button>
                                <button onclick="filterByPrice(10, 25)" class="price-filter w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                                    $10 - $25
                                </button>
                                <button onclick="filterByPrice(25, 50)" class="price-filter w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                                    $25 - $50
                                </button>
                                <button onclick="filterByPrice(50, 999)" class="price-filter w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                                    Over $50
                                </button>
                            </div>

                            <!-- Stock Filter -->
                            <div class="border-t my-4"></div>
                            <h4 class="font-semibold text-gray-900 mb-3">Availability</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" id="in-stock-filter" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2 text-sm text-gray-700">In Stock Only</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="featured-filter" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2 text-sm text-gray-700">Featured Products</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Area -->
                    <div class="flex-1">
                        <!-- Results Header -->
                        <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div id="results-info" class="text-gray-600">
                                    <span id="products-count">Loading products...</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-500">View:</span>
                                    <button onclick="setViewMode('grid')" id="grid-view-btn" class="p-2 rounded-md bg-primary-100 text-primary-600">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                        </svg>
                                    </button>
                                    <button onclick="setViewMode('list')" id="list-view-btn" class="p-2 rounded-md text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Loading State -->
                        <div id="products-loading" class="text-center py-12 hidden">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                            <p class="mt-4 text-gray-600">Loading products...</p>
                        </div>

                        <!-- Error State -->
                        <div id="products-error" class="text-center py-12 text-red-600 hidden">
                            <div class="text-6xl mb-4">⚠️</div>
                            <h3 class="text-xl font-semibold mb-2">Failed to load products</h3>
                            <p class="text-gray-600 mb-4">Please check your connection and try again.</p>
                            <button onclick="loadProducts()" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg">
                                Retry Loading
                            </button>
                        </div>

                        <!-- Empty State -->
                        <div id="products-empty" class="text-center py-12 hidden">
                            <div class="text-6xl mb-4">🔍</div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">No Products Found</h3>
                            <p class="text-gray-600 mb-4">Try adjusting your search or filter criteria.</p>
                            <button onclick="clearFilters()" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg">
                                Clear Filters
                            </button>
                        </div>

                        <!-- Products Grid -->
                        <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                            <!-- Products will be populated here -->
                        </div>

                        <!-- Products List View -->
                        <div id="products-list" class="space-y-4 hidden">
                            <!-- List view products will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkout Section -->
        <div id="checkout-section" class="max-w-4xl mx-auto p-6 hidden">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Checkout</h2>
                    <button onclick="hideCheckout()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Checkout Form -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Shipping Information</h3>
                        <form id="checkout-form" class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                    <input type="text" id="first-name" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                    <input type="text" id="last-name" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" id="checkout-email" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" id="checkout-phone" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
                                <input type="text" id="checkout-address" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                                    <input type="text" id="checkout-city" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">State *</label>
                                    <input type="text" id="checkout-state" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">ZIP Code *</label>
                                    <input type="text" id="checkout-zip" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Order Notes</label>
                                <textarea id="checkout-notes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="Special instructions for your order..."></textarea>
                            </div>

                            <div class="border-t pt-4">
                                <h3 class="text-lg font-semibold mb-4">Payment Method</h3>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="payment-method" value="credit_card" checked class="mr-2">
                                        <span>💳 Credit Card</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="payment-method" value="paypal" class="mr-2">
                                        <span>🅿️ PayPal</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="payment-method" value="cod" class="mr-2">
                                        <span>💵 Cash on Delivery</span>
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Order Summary -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Order Summary</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div id="checkout-items" class="space-y-3 mb-4">
                                <!-- Order items will be populated here -->
                            </div>

                            <div class="border-t pt-4 space-y-2">
                                <div class="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span id="checkout-subtotal">$0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Tax (8.25%):</span>
                                    <span id="checkout-tax">$0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Shipping:</span>
                                    <span id="checkout-shipping">$9.99</span>
                                </div>
                                <div class="flex justify-between font-bold text-lg border-t pt-2">
                                    <span>Total:</span>
                                    <span id="checkout-total">$0.00</span>
                                </div>
                            </div>

                            <button onclick="processCheckout()" class="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold mt-4">
                                Place Order
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Surgical Shopping Cart -->
        <div id="cart-section" class="hidden">
            <!-- Cart Header -->
            <div class="bg-white border-b">
                <div class="max-w-6xl mx-auto px-4 py-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">🏥 Surgical Cart</h1>
                            <p class="mt-2 text-gray-600">Professional medical supplies for your facility</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="saveCartAsKit()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                                💾 Save as Kit
                            </button>
                            <button onclick="hideCart()" class="text-gray-500 hover:text-gray-700">
                                <span class="text-2xl">&times;</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-6xl mx-auto px-4 py-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Cart Items Section -->
                    <div class="lg:col-span-2">
                        <!-- Cart Controls -->
                        <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <h3 class="text-lg font-semibold text-gray-900">Cart Items</h3>
                                    <span id="cart-item-count" class="bg-primary-100 text-primary-800 px-2 py-1 rounded-full text-sm font-medium">0 items</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="bulkEditMode()" class="text-sm text-primary-600 hover:text-primary-700 font-medium">
                                        ✏️ Bulk Edit
                                    </button>
                                    <button onclick="clearCart()" class="text-sm text-red-600 hover:text-red-700 font-medium">
                                        🗑️ Clear All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Cart Items List -->
                        <div id="cart-items" class="space-y-4">
                            <!-- Cart items will be populated here -->
                        </div>

                        <!-- Empty Cart State -->
                        <div id="cart-empty" class="bg-white rounded-lg shadow-sm border p-8 text-center hidden">
                            <div class="text-6xl mb-4">🛒</div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">Your surgical cart is empty</h3>
                            <p class="text-gray-600 mb-6">Add medical supplies to get started with your order</p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <button onclick="hideCart(); showProducts();" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium">
                                    Browse Medical Supplies
                                </button>
                                <button onclick="loadSavedKits()" class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-medium">
                                    Load Saved Kit
                                </button>
                            </div>
                        </div>

                        <!-- Bulk Actions Bar (Hidden by default) -->
                        <div id="bulk-actions-bar" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4 hidden">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm font-medium text-blue-900">Bulk Actions:</span>
                                    <button onclick="bulkUpdateQuantity()" class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                        Update Quantities
                                    </button>
                                    <button onclick="bulkRemoveSelected()" class="text-sm text-red-600 hover:text-red-700 font-medium">
                                        Remove Selected
                                    </button>
                                </div>
                                <button onclick="exitBulkMode()" class="text-sm text-gray-600 hover:text-gray-700">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Summary Sidebar -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-sm border p-6 sticky top-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

                            <!-- Quick Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="text-center p-3 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-primary-600" id="total-items">0</div>
                                    <div class="text-sm text-gray-600">Items</div>
                                </div>
                                <div class="text-center p-3 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600" id="total-savings">$0</div>
                                    <div class="text-sm text-gray-600">Savings</div>
                                </div>
                            </div>

                            <!-- Price Breakdown -->
                            <div id="cart-summary" class="space-y-3 mb-6 hidden">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Subtotal:</span>
                                    <span id="cart-subtotal">$0.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Bulk Discounts:</span>
                                    <span id="cart-discounts" class="text-green-600">-$0.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Tax (8.25%):</span>
                                    <span id="cart-tax">$0.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Shipping:</span>
                                    <span id="cart-shipping">$9.99</span>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-semibold text-gray-900">Total:</span>
                                        <span id="cart-total" class="text-2xl font-bold text-primary-600">$0.00</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Medical Facility Info -->
                            <div class="bg-blue-50 rounded-lg p-4 mb-6">
                                <h4 class="font-semibold text-blue-900 mb-2">🏥 Medical Facility</h4>
                                <div class="text-sm text-blue-800">
                                    <p>Grace Medical Limited</p>
                                    <p>Tax Exempt: Yes</p>
                                    <p>Account: Professional</p>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-3">
                                <button onclick="proceedToCheckout()"
                                        id="checkout-btn"
                                        class="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                    🛒 Proceed to Checkout
                                </button>

                                <button onclick="requestQuote()" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-medium transition-colors">
                                    📋 Request Quote
                                </button>

                                <button onclick="saveForLater()" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-medium transition-colors">
                                    💾 Save for Later
                                </button>
                            </div>

                            <!-- Shipping Info -->
                            <div class="mt-6 text-xs text-gray-500">
                                <p>🚚 Free shipping on orders over $75</p>
                                <p>📦 Same-day shipping available</p>
                                <p>🔒 Secure medical supply handling</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Section -->
        <div id="orders-section" class="hidden">
            <div class="max-w-7xl mx-auto px-4 py-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">My Orders</h2>
                    <div class="flex items-center space-x-4">
                        <button onclick="refreshOrders()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                            🔄 Refresh
                        </button>
                        <button onclick="showHome()" class="text-primary-600 hover:text-primary-700 font-medium">← Back to Home</button>
                    </div>
                </div>

                <!-- Order Status Legend -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Order Status Guide</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 rounded-full bg-gray-500"></div>
                            <span class="text-sm text-gray-700">Pending</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                            <span class="text-sm text-gray-700">Processing</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 rounded-full bg-green-500"></div>
                            <span class="text-sm text-gray-700">Shipped</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 rounded-full bg-green-600"></div>
                            <span class="text-sm text-gray-700">Complete</span>
                        </div>
                    </div>
                </div>

                <div id="orders-loading" class="text-center py-8 hidden">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">Loading orders...</p>
                </div>

                <div id="orders-empty" class="text-center py-12 hidden">
                    <div class="text-6xl mb-4">📦</div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">No Orders Yet</h3>
                    <p class="text-gray-600 mb-6">You haven't placed any orders yet.</p>
                    <button onclick="showProducts()" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg">
                        Start Shopping
                    </button>
                </div>

                <div id="orders-list" class="space-y-6">
                    <!-- Orders will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Notifications Section -->
        <div id="notifications-section" class="hidden">
            <div class="max-w-7xl mx-auto px-4 py-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">Notifications</h2>
                    <div class="flex items-center space-x-4">
                        <button onclick="markAllNotificationsRead()" class="text-primary-600 hover:text-primary-700 font-medium text-sm">
                            Mark All Read
                        </button>
                        <button onclick="showHome()" class="text-primary-600 hover:text-primary-700 font-medium">← Back to Home</button>
                    </div>
                </div>

                <!-- Notification Filters -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                    <div class="flex flex-wrap gap-4 items-center">
                        <span class="text-sm font-medium text-gray-700">Filter by:</span>
                        <button onclick="filterNotifications('all')" id="filter-all" class="notification-filter active px-3 py-1 rounded-full text-sm font-medium bg-primary-600 text-white">
                            All
                        </button>
                        <button onclick="filterNotifications('order')" id="filter-order" class="notification-filter px-3 py-1 rounded-full text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300">
                            Orders
                        </button>
                        <button onclick="filterNotifications('shipping')" id="filter-shipping" class="notification-filter px-3 py-1 rounded-full text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300">
                            Shipping
                        </button>
                        <button onclick="filterNotifications('system')" id="filter-system" class="notification-filter px-3 py-1 rounded-full text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300">
                            System
                        </button>
                        <div class="ml-auto">
                            <span class="text-sm text-gray-500">
                                <span id="unread-count">0</span> unread notifications
                            </span>
                        </div>
                    </div>
                </div>

                <div id="notifications-loading" class="text-center py-8 hidden">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">Loading notifications...</p>
                </div>

                <div id="notifications-empty" class="text-center py-12 hidden">
                    <div class="text-6xl mb-4">🔔</div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">No Notifications</h3>
                    <p class="text-gray-600 mb-6">You don't have any notifications yet.</p>
                    <button onclick="showProducts()" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg">
                        Start Shopping
                    </button>
                </div>

                <div id="notifications-list" class="space-y-4">
                    <!-- Notifications will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Admin Section -->
        <div id="admin-section" class="hidden">
            <div class="max-w-7xl mx-auto px-4 py-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">Admin Dashboard</h2>
                    <button onclick="showHome()" class="text-primary-600 hover:text-primary-700 font-medium">← Back to Home</button>
                </div>

                <!-- Admin Navigation Tabs -->
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8">
                        <button onclick="showAdminTab('overview')" id="tab-overview" class="admin-tab border-b-2 border-purple-500 text-purple-600 py-2 px-1 text-sm font-medium">
                            Overview
                        </button>
                        <button onclick="showAdminTab('products')" id="tab-products" class="admin-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium">
                            Products
                        </button>
                        <button onclick="showAdminTab('orders')" id="tab-orders" class="admin-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium">
                            Orders
                        </button>
                        <button onclick="showAdminTab('stock')" id="tab-stock" class="admin-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium">
                            Stock Alerts
                        </button>
                    </nav>
                </div>

                <!-- Admin Overview Tab -->
                <div id="admin-overview" class="admin-tab-content">
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm">📦</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Products</p>
                                    <p id="stat-products" class="text-2xl font-semibold text-gray-900">-</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm">📋</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Orders</p>
                                    <p id="stat-orders" class="text-2xl font-semibold text-gray-900">-</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm">⚠️</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Low Stock Items</p>
                                    <p id="stat-low-stock" class="text-2xl font-semibold text-gray-900">-</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm">💰</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                    <p id="stat-revenue" class="text-2xl font-semibold text-gray-900">-</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Orders</h3>
                        <div id="admin-recent-orders">
                            <!-- Recent orders will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Admin Products Tab -->
                <div id="admin-products" class="admin-tab-content hidden">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Product Management</h3>
                            <button onclick="showAddProductForm()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                                Add New Product
                            </button>
                        </div>

                        <!-- Search and Filter Controls -->
                        <div class="mb-6 grid md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Search Products</label>
                                <input type="text" id="product-search" placeholder="Search by name, category, manufacturer..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                                       onkeyup="filterProducts()">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                <select id="category-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                                        onchange="filterProducts()">
                                    <option value="">All Categories</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
                                <select id="stock-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                                        onchange="filterProducts()">
                                    <option value="">All Stock Levels</option>
                                    <option value="low">Low Stock</option>
                                    <option value="normal">Normal Stock</option>
                                    <option value="out">Out of Stock</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                                <select id="sort-products" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                                        onchange="sortProducts()">
                                    <option value="name">Name (A-Z)</option>
                                    <option value="name-desc">Name (Z-A)</option>
                                    <option value="price">Price (Low to High)</option>
                                    <option value="price-desc">Price (High to Low)</option>
                                    <option value="stock">Stock (Low to High)</option>
                                    <option value="stock-desc">Stock (High to Low)</option>
                                </select>
                            </div>
                        </div>

                        <div id="admin-products-list">
                            <!-- Products management will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Admin Orders Tab -->
                <div id="admin-orders" class="admin-tab-content hidden">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Order Management</h3>
                        <div id="admin-orders-list">
                            <!-- Orders management will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Admin Stock Tab -->
                <div id="admin-stock" class="admin-tab-content hidden">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Stock Alerts & Daily Monitoring</h3>
                            <button onclick="runDailyCron()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                🕐 Run Daily Check
                            </button>
                        </div>
                        <div id="admin-stock-alerts">
                            <!-- Stock alerts will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentUser = null;
        let products = [];
        let cart = JSON.parse(localStorage.getItem('gracemedical_cart') || '[]');

        // Navigation functions
        function showHome() {
            document.getElementById('main-content').classList.remove('hidden');
            document.getElementById('products-section').classList.add('hidden');
            document.getElementById('orders-section').classList.add('hidden');
            document.getElementById('notifications-section').classList.add('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            document.getElementById('profile-section').classList.add('hidden');
        }

        function showProducts() {
            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.remove('hidden');
            document.getElementById('orders-section').classList.add('hidden');
            document.getElementById('notifications-section').classList.add('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            document.getElementById('profile-section').classList.add('hidden');
            document.getElementById('cart-section').classList.add('hidden');
            loadProducts();
        }

        function showCart() {
            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.add('hidden');
            document.getElementById('orders-section').classList.add('hidden');
            document.getElementById('notifications-section').classList.add('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            document.getElementById('profile-section').classList.add('hidden');
            document.getElementById('cart-section').classList.remove('hidden');
            loadCart();
        }

        function hideCart() {
            document.getElementById('cart-section').classList.add('hidden');
            document.getElementById('main-content').classList.remove('hidden');
        }

        // E-commerce Cart Functions
        async function loadCart() {
            try {
                const response = await fetch('/api/cart');
                const data = await response.json();

                if (response.ok) {
                    displayCart(data);
                    updateCartBadge(data.item_count);
                } else {
                    showToast('Failed to load cart: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error loading cart:', error);
                showToast('Error loading cart: ' + error.message, 'error');
            }
        }

        function displayCart(cartData) {
            const cartItems = document.getElementById('cart-items');
            const cartEmpty = document.getElementById('cart-empty');
            const cartSummary = document.getElementById('cart-summary');
            const cartItemCount = document.getElementById('cart-item-count');
            const totalItems = document.getElementById('total-items');

            if (cartData.items.length === 0) {
                cartItems.innerHTML = '';
                cartEmpty.classList.remove('hidden');
                cartSummary.classList.add('hidden');
                cartItemCount.textContent = '0 items';
                totalItems.textContent = '0';
                return;
            }

            cartEmpty.classList.add('hidden');
            cartSummary.classList.remove('hidden');

            // Update item counts
            const totalItemCount = cartData.items.reduce((sum, item) => sum + item.quantity, 0);
            cartItemCount.textContent = `${totalItemCount} items`;
            totalItems.textContent = totalItemCount;

            // Display professional medical supply cart items
            cartItems.innerHTML = cartData.items.map(item => `
                <div class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-start space-x-4">
                        <!-- Product Image -->
                        <div class="flex-shrink-0">
                            <img src="${item.image_url || 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop'}"
                                 alt="${item.name}"
                                 class="w-20 h-20 object-cover rounded-lg border"
                                 onerror="this.src='https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop'">
                        </div>

                        <!-- Product Details -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <!-- Product Category -->
                                    <div class="text-xs text-primary-600 font-medium mb-1">
                                        ${getProductCategory(item)}
                                    </div>

                                    <!-- Product Name -->
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">${item.name}</h4>

                                    <!-- Medical Info -->
                                    <div class="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                                        <span class="flex items-center">
                                            📦 ${getPackagingInfo({ name: item.name })}
                                        </span>
                                        <span class="flex items-center">
                                            🏥 Medical Grade
                                        </span>
                                        <span class="flex items-center">
                                            ✅ Sterile
                                        </span>
                                    </div>

                                    <!-- Stock Status -->
                                    <div class="flex items-center text-sm mb-4">
                                        ${item.stock_quantity > 10 ?
                                            `<span class="text-green-600 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                In Stock (${item.stock_quantity} available)
                                            </span>` :
                                            `<span class="text-orange-600 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                                Low Stock (${item.stock_quantity} left)
                                            </span>`
                                        }
                                    </div>
                                </div>

                                <!-- Bulk Edit Checkbox -->
                                <div class="bulk-edit-controls hidden">
                                    <input type="checkbox" class="bulk-select-item rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                           data-item-id="${item.id}">
                                </div>
                            </div>

                            <!-- Quantity and Pricing Controls -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <!-- Quantity Controls -->
                                    <div class="flex items-center border border-gray-300 rounded-lg">
                                        <button onclick="updateCartQuantity(${item.id}, ${item.quantity - 1})"
                                                class="px-3 py-2 hover:bg-gray-100 text-gray-600 transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                            </svg>
                                        </button>
                                        <input type="number" value="${item.quantity}" min="1" max="${item.stock_quantity}"
                                               class="w-16 text-center border-0 focus:ring-0 text-sm font-medium"
                                               onchange="updateCartQuantity(${item.id}, this.value)">
                                        <button onclick="updateCartQuantity(${item.id}, ${item.quantity + 1})"
                                                class="px-3 py-2 hover:bg-gray-100 text-gray-600 transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Quick Actions -->
                                    <div class="flex items-center space-x-2">
                                        <button onclick="saveItemForLater(${item.id})"
                                                class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                            💾 Save for Later
                                        </button>
                                        <button onclick="removeFromCart(${item.id})"
                                                class="text-sm text-red-600 hover:text-red-700 font-medium">
                                            🗑️ Remove
                                        </button>
                                    </div>
                                </div>

                                <!-- Pricing -->
                                <div class="text-right">
                                    <div class="text-sm text-gray-600 mb-1">
                                        $${item.price.toFixed(2)} each
                                    </div>
                                    <div class="text-lg font-bold text-gray-900">
                                        $${item.subtotal.toFixed(2)}
                                    </div>
                                    ${item.bulk_price && item.bulk_price < item.price && item.quantity >= 10 ?
                                        `<div class="text-xs text-green-600">
                                            Bulk discount applied!
                                        </div>` : ''
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            // Update cart totals
            updateCartTotals(cartData);
        }

        function getProductCategory(item) {
            // Extract category from item or use default
            return item.category || 'Medical Supplies';
        }

        function updateCartTotals(cartData) {
            const subtotal = cartData.total;
            const discounts = calculateBulkDiscounts(cartData.items);
            const taxRate = 0.0825; // 8.25%
            const tax = (subtotal - discounts) * taxRate;
            const shipping = subtotal >= 75 ? 0 : 9.99;
            const total = subtotal - discounts + tax + shipping;

            // Update display
            document.getElementById('cart-subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('cart-discounts').textContent = `-$${discounts.toFixed(2)}`;
            document.getElementById('cart-tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('cart-shipping').textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
            document.getElementById('cart-total').textContent = `$${total.toFixed(2)}`;
            document.getElementById('total-savings').textContent = `$${discounts.toFixed(2)}`;

            // Update shipping message
            const shippingElement = document.getElementById('cart-shipping');
            if (shipping === 0) {
                shippingElement.parentElement.classList.add('text-green-600');
            }
        }

        function calculateBulkDiscounts(items) {
            let totalDiscount = 0;
            items.forEach(item => {
                if (item.bulk_price && item.bulk_price < item.price && item.quantity >= 10) {
                    totalDiscount += (item.price - item.bulk_price) * item.quantity;
                }
            });
            return totalDiscount;
        }

        // Advanced Surgical Cart Features
        function saveCartAsKit() {
            if (!cart || cart.length === 0) {
                showToast('Cart is empty. Add items before saving as kit.', 'error');
                return;
            }

            const kitName = prompt('Enter a name for this surgical kit:');
            if (!kitName) return;

            const kit = {
                name: kitName,
                items: cart.map(item => ({
                    product_id: item.product_id,
                    quantity: item.quantity,
                    name: item.name
                })),
                created_at: new Date().toISOString(),
                total_items: cart.reduce((sum, item) => sum + item.quantity, 0)
            };

            // Save to localStorage for now (could be enhanced to save to backend)
            const savedKits = JSON.parse(localStorage.getItem('surgicalKits') || '[]');
            savedKits.push(kit);
            localStorage.setItem('surgicalKits', JSON.stringify(savedKits));

            showToast(`Surgical kit "${kitName}" saved successfully!`, 'success');
        }

        function loadSavedKits() {
            const savedKits = JSON.parse(localStorage.getItem('surgicalKits') || '[]');

            if (savedKits.length === 0) {
                showToast('No saved surgical kits found.', 'info');
                return;
            }

            // Create modal to show saved kits
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-bold text-gray-900">🏥 Saved Surgical Kits</h3>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                                <span class="text-2xl">&times;</span>
                            </button>
                        </div>

                        <div class="space-y-4">
                            ${savedKits.map((kit, index) => `
                                <div class="border rounded-lg p-4 hover:bg-gray-50">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-900">${kit.name}</h4>
                                            <p class="text-sm text-gray-600 mt-1">
                                                ${kit.total_items} items • Created ${new Date(kit.created_at).toLocaleDateString()}
                                            </p>
                                            <div class="mt-2 text-xs text-gray-500">
                                                ${kit.items.slice(0, 3).map(item => item.name).join(', ')}
                                                ${kit.items.length > 3 ? ` and ${kit.items.length - 3} more...` : ''}
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button onclick="loadKit(${index}); this.closest('.fixed').remove();"
                                                    class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm">
                                                Load Kit
                                            </button>
                                            <button onclick="deleteKit(${index}); this.closest('.fixed').remove(); loadSavedKits();"
                                                    class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function loadKit(kitIndex) {
            const savedKits = JSON.parse(localStorage.getItem('surgicalKits') || '[]');
            const kit = savedKits[kitIndex];

            if (!kit) {
                showToast('Kit not found.', 'error');
                return;
            }

            // Clear current cart
            clearCart();

            // Add kit items to cart
            kit.items.forEach(item => {
                addToCart(item.product_id, item.quantity);
            });

            showToast(`Loaded surgical kit: ${kit.name}`, 'success');
        }

        function deleteKit(kitIndex) {
            const savedKits = JSON.parse(localStorage.getItem('surgicalKits') || '[]');
            const kit = savedKits[kitIndex];

            if (confirm(`Delete surgical kit "${kit.name}"?`)) {
                savedKits.splice(kitIndex, 1);
                localStorage.setItem('surgicalKits', JSON.stringify(savedKits));
                showToast('Surgical kit deleted.', 'success');
            }
        }

        // Bulk Edit Functions
        let bulkEditModeActive = false;

        function bulkEditMode() {
            bulkEditModeActive = true;
            document.getElementById('bulk-actions-bar').classList.remove('hidden');
            document.querySelectorAll('.bulk-edit-controls').forEach(el => {
                el.classList.remove('hidden');
            });
            showToast('Bulk edit mode activated. Select items to edit.', 'info');
        }

        function exitBulkMode() {
            bulkEditModeActive = false;
            document.getElementById('bulk-actions-bar').classList.add('hidden');
            document.querySelectorAll('.bulk-edit-controls').forEach(el => {
                el.classList.add('hidden');
            });
            document.querySelectorAll('.bulk-select-item').forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        function bulkUpdateQuantity() {
            const selectedItems = document.querySelectorAll('.bulk-select-item:checked');
            if (selectedItems.length === 0) {
                showToast('Please select items to update.', 'error');
                return;
            }

            const newQuantity = prompt('Enter new quantity for selected items:');
            if (!newQuantity || isNaN(newQuantity) || newQuantity < 1) {
                showToast('Please enter a valid quantity.', 'error');
                return;
            }

            selectedItems.forEach(checkbox => {
                const itemId = checkbox.getAttribute('data-item-id');
                updateCartQuantity(itemId, parseInt(newQuantity));
            });

            exitBulkMode();
            showToast(`Updated ${selectedItems.length} items to quantity ${newQuantity}.`, 'success');
        }

        function bulkRemoveSelected() {
            const selectedItems = document.querySelectorAll('.bulk-select-item:checked');
            if (selectedItems.length === 0) {
                showToast('Please select items to remove.', 'error');
                return;
            }

            if (confirm(`Remove ${selectedItems.length} selected items from cart?`)) {
                selectedItems.forEach(checkbox => {
                    const itemId = checkbox.getAttribute('data-item-id');
                    removeFromCart(itemId);
                });

                exitBulkMode();
                showToast(`Removed ${selectedItems.length} items from cart.`, 'success');
            }
        }

        // Additional Cart Functions
        function saveItemForLater(itemId) {
            // Move item to saved for later list
            const savedItems = JSON.parse(localStorage.getItem('savedForLater') || '[]');
            const cartItem = cart.find(item => item.id === itemId);

            if (cartItem) {
                savedItems.push(cartItem);
                localStorage.setItem('savedForLater', JSON.stringify(savedItems));
                removeFromCart(itemId);
                showToast('Item saved for later.', 'success');
            }
        }

        function saveForLater() {
            if (!cart || cart.length === 0) {
                showToast('Cart is empty.', 'error');
                return;
            }

            const savedItems = JSON.parse(localStorage.getItem('savedForLater') || '[]');
            savedItems.push(...cart);
            localStorage.setItem('savedForLater', JSON.stringify(savedItems));

            clearCart();
            showToast('All items saved for later.', 'success');
        }

        function requestQuote() {
            if (!cart || cart.length === 0) {
                showToast('Cart is empty. Add items to request a quote.', 'error');
                return;
            }

            // Create quote request modal
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg max-w-md w-full mx-4">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">📋 Request Quote</h3>
                        <form onsubmit="submitQuoteRequest(event)">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Facility Name</label>
                                    <input type="text" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                                    <input type="email" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Special Requirements</label>
                                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500" placeholder="Delivery timeline, bulk discounts, etc."></textarea>
                                </div>
                            </div>
                            <div class="flex gap-3 mt-6">
                                <button type="button" onclick="this.closest('.fixed').remove()"
                                        class="flex-1 border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg">
                                    Cancel
                                </button>
                                <button type="submit" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg">
                                    Send Quote Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function submitQuoteRequest(event) {
            event.preventDefault();
            // Here you would normally send the quote request to the backend
            showToast('Quote request submitted! We\'ll contact you within 24 hours.', 'success');
            event.target.closest('.fixed').remove();
        }

        async function addToCart(productId, quantity = 1) {
            try {
                const response = await fetch('/api/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        quantity: quantity
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Item added to cart!', 'success');
                    loadCart(); // Refresh cart
                } else {
                    showToast('Failed to add to cart: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error adding to cart:', error);
                showToast('Error adding to cart: ' + error.message, 'error');
            }
        }

        async function updateCartQuantity(itemId, newQuantity) {
            try {
                const response = await fetch(`/api/cart/update/${itemId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        quantity: newQuantity
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    loadCart(); // Refresh cart
                } else {
                    showToast('Failed to update cart: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error updating cart:', error);
                showToast('Error updating cart: ' + error.message, 'error');
            }
        }

        async function removeFromCart(itemId) {
            try {
                const response = await fetch(`/api/cart/remove/${itemId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Item removed from cart', 'success');
                    loadCart(); // Refresh cart
                } else {
                    showToast('Failed to remove item: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error removing from cart:', error);
                showToast('Error removing item: ' + error.message, 'error');
            }
        }

        function updateCartBadge(count) {
            const badge = document.getElementById('cart-badge');
            if (count > 0) {
                badge.textContent = count;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }

        // Checkout Functions
        function showCheckout() {
            document.getElementById('cart-section').classList.add('hidden');
            document.getElementById('checkout-section').classList.remove('hidden');
            loadCheckoutSummary();
        }

        function hideCheckout() {
            document.getElementById('checkout-section').classList.add('hidden');
            document.getElementById('main-content').classList.remove('hidden');
        }

        function proceedToCheckout() {
            showCheckout();
        }

        async function loadCheckoutSummary() {
            try {
                const response = await fetch('/api/cart');
                const data = await response.json();

                if (response.ok) {
                    displayCheckoutSummary(data);
                } else {
                    showToast('Failed to load checkout summary: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error loading checkout summary:', error);
                showToast('Error loading checkout summary: ' + error.message, 'error');
            }
        }

        function displayCheckoutSummary(cartData) {
            const checkoutItems = document.getElementById('checkout-items');

            checkoutItems.innerHTML = cartData.items.map(item => `
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-2">
                        <img src="${item.image_url}" alt="${item.name}" class="w-10 h-10 object-cover rounded">
                        <div>
                            <p class="font-medium text-sm">${item.name}</p>
                            <p class="text-xs text-gray-500">Qty: ${item.quantity}</p>
                        </div>
                    </div>
                    <span class="font-medium">$${item.subtotal.toFixed(2)}</span>
                </div>
            `).join('');

            // Calculate totals
            const subtotal = cartData.total;
            const taxRate = 0.0825; // 8.25%
            const tax = subtotal * taxRate;
            const shipping = subtotal >= 75 ? 0 : 9.99; // Free shipping over $75
            const total = subtotal + tax + shipping;

            document.getElementById('checkout-subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('checkout-tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('checkout-shipping').textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
            document.getElementById('checkout-total').textContent = `$${total.toFixed(2)}`;
        }

        async function processCheckout() {
            try {
                // Enhanced medical facility checkout
                const formData = {
                    // Basic info
                    first_name: document.getElementById('first-name').value,
                    last_name: document.getElementById('last-name').value,
                    email: document.getElementById('checkout-email').value,
                    phone: document.getElementById('checkout-phone').value,

                    // Medical facility info
                    facility_name: document.getElementById('facility-name')?.value || 'Grace Medical Limited',
                    facility_type: document.getElementById('facility-type')?.value || 'hospital',
                    tax_exempt: document.getElementById('tax-exempt')?.checked || true,
                    medical_license: document.getElementById('medical-license')?.value || '',

                    // Shipping
                    shipping_address: `${document.getElementById('checkout-address').value}, ${document.getElementById('checkout-city').value}, ${document.getElementById('checkout-state').value} ${document.getElementById('checkout-zip').value}`,
                    delivery_instructions: document.getElementById('delivery-instructions')?.value || '',
                    preferred_delivery_time: document.getElementById('delivery-time')?.value || 'business_hours',

                    // Order details
                    notes: document.getElementById('checkout-notes').value,
                    payment_method: document.querySelector('input[name="payment-method"]:checked').value,
                    purchase_order: document.getElementById('purchase-order')?.value || '',

                    // Medical specific
                    sterile_packaging_required: document.getElementById('sterile-packaging')?.checked || true,
                    temperature_controlled: document.getElementById('temp-controlled')?.checked || false,
                    rush_order: document.getElementById('rush-order')?.checked || false
                };

                // Enhanced validation for medical facilities
                if (!formData.first_name || !formData.last_name || !formData.email || !formData.shipping_address) {
                    showToast('Please fill in all required fields', 'error');
                    return;
                }

                // Show processing state
                const checkoutBtn = document.getElementById('checkout-btn');
                const originalText = checkoutBtn.textContent;
                checkoutBtn.textContent = 'Processing Order...';
                checkoutBtn.disabled = true;

                const response = await fetch('/api/checkout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Medical supply order placed successfully!', 'success');

                    // Show enhanced order confirmation
                    showMedicalOrderConfirmation(data);

                    // Clear cart and update display
                    updateCartBadge(0);

                    // Send confirmation email (mock)
                    sendOrderConfirmationEmail(data, formData);

                } else {
                    showToast('Failed to place order: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error processing checkout:', error);
                showToast('Error processing checkout: ' + error.message, 'error');
            } finally {
                // Reset button
                const checkoutBtn = document.getElementById('checkout-btn');
                checkoutBtn.textContent = originalText;
                checkoutBtn.disabled = false;
            }
        }

        function showMedicalOrderConfirmation(orderData) {
            const confirmationHtml = `
                <div id="order-confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 p-8">
                        <div class="text-center">
                            <!-- Success Icon -->
                            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>

                            <h3 class="text-2xl font-bold text-gray-900 mb-2">🏥 Medical Order Confirmed!</h3>
                            <p class="text-gray-600 mb-6">Your medical supply order has been successfully placed and is being processed.</p>

                            <!-- Order Details -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6 text-left">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Order Number</p>
                                        <p class="text-lg font-bold text-primary-600">${orderData.order_number}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Total Amount</p>
                                        <p class="text-lg font-bold text-gray-900">$${orderData.total_amount.toFixed(2)}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Estimated Delivery</p>
                                        <p class="text-sm text-gray-600">1-2 business days</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Order Status</p>
                                        <p class="text-sm text-blue-600">Processing</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Medical Facility Features -->
                            <div class="bg-blue-50 rounded-lg p-4 mb-6">
                                <h4 class="font-semibold text-blue-900 mb-2">🏥 Medical Facility Benefits</h4>
                                <div class="text-sm text-blue-800 space-y-1">
                                    <p>✅ Sterile packaging maintained</p>
                                    <p>✅ Temperature-controlled shipping</p>
                                    <p>✅ Medical-grade handling protocols</p>
                                    <p>✅ Compliance documentation included</p>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-3">
                                <button onclick="closeOrderConfirmation(); showOrders();"
                                        class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium">
                                    📋 View Order Details
                                </button>
                                <button onclick="closeOrderConfirmation(); showProducts();"
                                        class="flex-1 border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-medium">
                                    🛒 Continue Shopping
                                </button>
                                <button onclick="printOrderConfirmation('${orderData.order_number}')"
                                        class="flex-1 border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-medium">
                                    🖨️ Print Receipt
                                </button>
                            </div>

                            <!-- Contact Info -->
                            <div class="mt-6 text-xs text-gray-500">
                                <p>Questions? Contact our medical supply <NAME_EMAIL> or (555) 123-4567</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', confirmationHtml);
        }

        function sendOrderConfirmationEmail(orderData, customerData) {
            // Mock email sending - in real implementation, this would call backend
            console.log('Sending order confirmation email:', {
                to: customerData.email,
                order_number: orderData.order_number,
                facility: customerData.facility_name,
                total: orderData.total_amount
            });

            // Show email sent notification
            setTimeout(() => {
                showToast('📧 Order confirmation email sent!', 'success');
            }, 2000);
        }

        function printOrderConfirmation(orderNumber) {
            // Create printable order confirmation
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Order Confirmation - ${orderNumber}</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; }
                            .order-info { margin: 20px 0; }
                            .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            .items-table th { background-color: #f2f2f2; }
                            .total { font-weight: bold; font-size: 18px; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>🏥 Grace Medical Limited</h1>
                            <h2>Medical Supply Order Confirmation</h2>
                            <p>Order #: ${orderNumber}</p>
                            <p>Date: ${new Date().toLocaleDateString()}</p>
                        </div>
                        <div class="order-info">
                            <p><strong>Status:</strong> Processing</p>
                            <p><strong>Estimated Delivery:</strong> 1-2 business days</p>
                            <p><strong>Special Handling:</strong> Medical-grade sterile packaging</p>
                        </div>
                        <p>Thank you for your medical supply order. This confirmation serves as your receipt.</p>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function showOrderConfirmation(orderData) {
            const confirmationHtml = `
                <div id="order-confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg max-w-md w-full mx-4 p-6">
                        <div class="text-center">
                            <div class="text-6xl mb-4">✅</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Order Confirmed!</h3>
                            <p class="text-gray-600 mb-4">Thank you for your order. We'll send you a confirmation email shortly.</p>

                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <p><strong>Order Number:</strong> ${orderData.order_number}</p>
                                <p><strong>Total:</strong> $${orderData.total_amount.toFixed(2)}</p>
                            </div>

                            <div class="flex gap-3">
                                <button onclick="closeOrderConfirmation(); showOrders();" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg">
                                    View Orders
                                </button>
                                <button onclick="closeOrderConfirmation(); showProducts();" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg">
                                    Continue Shopping
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', confirmationHtml);
        }

        function closeOrderConfirmation() {
            const modal = document.getElementById('order-confirmation-modal');
            if (modal) {
                modal.remove();
            }
            hideCheckout();
        }

        function showOrders() {
            if (!currentUser) {
                showToast('Please login to view your orders', 'error');
                showLogin();
                return;
            }

            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.add('hidden');
            document.getElementById('orders-section').classList.remove('hidden');
            document.getElementById('notifications-section').classList.add('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            document.getElementById('profile-section').classList.add('hidden');
            loadOrders();
        }

        function showNotifications() {
            if (!currentUser) {
                showToast('Please login to view your notifications', 'error');
                showLogin();
                return;
            }

            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.add('hidden');
            document.getElementById('orders-section').classList.add('hidden');
            document.getElementById('notifications-section').classList.remove('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            document.getElementById('profile-section').classList.add('hidden');
            loadNotifications();
        }

        function showAdmin() {
            if (!currentUser || !currentUser.is_admin) {
                showToast('Admin access required', 'error');
                return;
            }

            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.add('hidden');
            document.getElementById('orders-section').classList.add('hidden');
            document.getElementById('notifications-section').classList.add('hidden');
            document.getElementById('admin-section').classList.remove('hidden');
            document.getElementById('profile-section').classList.add('hidden');
            showAdminTab('overview');
        }

        function showLogin() {
            document.getElementById('login-modal').classList.remove('hidden');
            document.getElementById('login-modal').classList.add('flex');
        }

        function hideLogin() {
            document.getElementById('login-modal').classList.add('hidden');
            document.getElementById('login-modal').classList.remove('flex');
        }

        function showRegister() {
            document.getElementById('register-modal').classList.remove('hidden');
            document.getElementById('register-modal').classList.add('flex');
        }

        function hideRegister() {
            document.getElementById('register-modal').classList.add('hidden');
            document.getElementById('register-modal').classList.remove('flex');
        }

        function showCart() {
            document.getElementById('cart-modal').classList.remove('hidden');
            document.getElementById('cart-modal').classList.add('flex');
            updateCartDisplay();
        }

        function hideCart() {
            document.getElementById('cart-modal').classList.add('hidden');
            document.getElementById('cart-modal').classList.remove('flex');
        }

        function showCheckout() {
            document.getElementById('checkout-modal').classList.remove('hidden');
            document.getElementById('checkout-modal').classList.add('flex');
            updateCheckoutDisplay();
        }

        function hideCheckout() {
            document.getElementById('checkout-modal').classList.add('hidden');
            document.getElementById('checkout-modal').classList.remove('flex');
        }

        // Authentication functions
        async function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    currentUser = data.user;
                    localStorage.setItem('gracemedical_user', JSON.stringify(data.user));
                    showToast(`Welcome back, ${data.user.first_name}!`, 'success');
                    hideLogin();
                    updateNavigation();
                } else {
                    showToast(data.error || 'Login failed', 'error');
                }
            } catch (error) {
                showToast('Login error: ' + error.message, 'error');
            }
        }

        async function handleRegister(event) {
            event.preventDefault();

            const formData = {
                first_name: document.getElementById('register-first-name').value,
                last_name: document.getElementById('register-last-name').value,
                email: document.getElementById('register-email').value,
                phone: document.getElementById('register-phone').value,
                password: document.getElementById('register-password').value
            };

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Registration successful! You can now log in.', 'success');
                    hideRegister();
                    showLogin();
                    // Pre-fill login email
                    document.getElementById('login-email').value = formData.email;
                } else {
                    showToast(data.error || 'Registration failed', 'error');
                }
            } catch (error) {
                showToast('Registration error: ' + error.message, 'error');
            }
        }

        function logout() {
            currentUser = null;
            localStorage.removeItem('gracemedical_user');

            // Clear cart on logout for security
            cart = [];
            localStorage.removeItem('gracemedical_cart');

            // Hide profile dropdown if open
            const dropdown = document.getElementById('profile-dropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
            }

            // Redirect to home page
            showHome();

            showToast('Logged out successfully', 'success');
            updateNavigation();
        }

        function toggleProfileMenu() {
            const dropdown = document.getElementById('profile-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
                console.log('Profile dropdown toggled:', !dropdown.classList.contains('hidden'));
            } else {
                console.error('Profile dropdown element not found');
            }
        }

        // Close profile dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const profileButton = document.getElementById('profile-button');
            const dropdown = document.getElementById('profile-dropdown');

            if (profileButton && dropdown && !profileButton.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });

        function showProfile() {
            if (!currentUser) {
                showToast('Please login to view your profile', 'error');
                showLogin();
                return;
            }

            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.add('hidden');
            document.getElementById('orders-section').classList.add('hidden');
            document.getElementById('notifications-section').classList.add('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            document.getElementById('profile-section').classList.remove('hidden');

            updateProfileDisplay();
        }

        function updateProfileDisplay() {
            if (!currentUser) return;

            // Get user initials
            const initials = getInitials(currentUser.first_name, currentUser.last_name);

            // Update profile header
            document.getElementById('profile-initials').textContent = initials;
            document.getElementById('profile-full-name').textContent = `${currentUser.first_name} ${currentUser.last_name}`;
            document.getElementById('profile-email').textContent = currentUser.email;
            document.getElementById('profile-role').textContent = currentUser.is_admin ? 'Administrator' : 'Doctor/Medical Professional';

            // Update profile details
            document.getElementById('profile-display-name').textContent = `${currentUser.first_name} ${currentUser.last_name}`;
            document.getElementById('profile-display-email').textContent = currentUser.email;
            document.getElementById('profile-display-role').textContent = currentUser.is_admin ? 'Administrator' : 'Doctor/Medical Professional';
            document.getElementById('profile-member-since').textContent = new Date().toLocaleDateString(); // Mock data

            // Load user statistics (mock data for now)
            loadUserStatistics();
        }

        function getInitials(firstName, lastName) {
            const first = firstName ? firstName.charAt(0).toUpperCase() : '';
            const last = lastName ? lastName.charAt(0).toUpperCase() : '';
            return first + last || 'U';
        }

        async function loadUserStatistics() {
            try {
                // Mock statistics - in a real app, this would fetch from API
                document.getElementById('profile-total-orders').textContent = '0';
                document.getElementById('profile-total-spent').textContent = '$0.00';
                document.getElementById('profile-last-order').textContent = 'Never';

                // If we had real order data, we could fetch it here
                // const response = await fetch('/api/user/statistics');
                // const stats = await response.json();
                // Update the display with real data
            } catch (error) {
                console.log('Error loading user statistics:', error);
            }
        }

        function updateNavigation() {
            const authButtons = document.getElementById('auth-buttons');
            const userMenu = document.getElementById('user-menu');
            const userName = document.getElementById('user-name');
            const userInitials = document.getElementById('user-initials');
            const userInitialsLarge = document.getElementById('user-initials-large');
            const userFullName = document.getElementById('user-full-name');
            const userEmail = document.getElementById('user-email');
            const userRole = document.getElementById('user-role');
            const adminButton = document.getElementById('admin-button');

            // User info display elements
            const userDisplayName = document.getElementById('user-display-name');
            const userDisplayRole = document.getElementById('user-display-role');

            if (currentUser) {
                authButtons.classList.add('hidden');
                userMenu.classList.remove('hidden');

                // Enhanced profile display
                const fullName = `${currentUser.first_name} ${currentUser.last_name}`;
                const initials = getInitials(currentUser.first_name, currentUser.last_name);
                const roleText = currentUser.is_admin ? 'Administrator' : 'Doctor';

                userName.textContent = fullName;
                if (userInitials) userInitials.textContent = initials;
                if (userInitialsLarge) userInitialsLarge.textContent = initials;
                if (userFullName) userFullName.textContent = fullName;
                if (userEmail) userEmail.textContent = currentUser.email;
                if (userRole) userRole.textContent = roleText;

                // Update user info display in header
                if (userDisplayName) userDisplayName.textContent = fullName;
                if (userDisplayRole) userDisplayRole.textContent = roleText;

                // Show admin button if user is admin
                if (currentUser.is_admin) {
                    adminButton.classList.remove('hidden');
                } else {
                    adminButton.classList.add('hidden');
                }
            } else {
                authButtons.classList.remove('hidden');
                userMenu.classList.add('hidden');
                adminButton.classList.add('hidden');
            }
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 4000);
        }

        // Enhanced Product Management for Medical Supply Catalog
        let products = [];
        let filteredProducts = [];
        let categories = [];
        let currentFilters = {
            category: '',
            search: '',
            priceMin: 0,
            priceMax: 999,
            inStockOnly: false,
            featuredOnly: false
        };
        let currentSort = 'name';
        let currentViewMode = 'grid';

        async function loadProducts() {
            showLoadingState();

            try {
                // Load products and categories
                const response = await fetch('/api/products');
                const data = await response.json();

                if (response.ok) {
                    products = Array.isArray(data) ? data : (data.products || []);

                    // Load categories
                    await loadCategories();

                    // Initialize display
                    populateCategories();
                    applyFiltersAndSort();
                    hideLoadingState();

                } else {
                    throw new Error(data.error || 'Failed to load products');
                }

            } catch (error) {
                console.error('Error loading products:', error);
                showErrorState();
                showToast('Error loading products: ' + error.message, 'error');
            }
        }

        async function loadCategories() {
            try {
                const response = await fetch('/api/categories');
                if (response.ok) {
                    const data = await response.json();
                    categories = Array.isArray(data) ? data : [];
                } else {
                    // Extract categories from products if API fails
                    const uniqueCategories = [...new Set(products.map(p => p.category).filter(Boolean))];
                    categories = uniqueCategories.map(name => ({ name, slug: name.toLowerCase().replace(/\s+/g, '-') }));
                }
            } catch (error) {
                console.error('Error loading categories:', error);
                // Extract categories from products as fallback
                const uniqueCategories = [...new Set(products.map(p => p.category).filter(Boolean))];
                categories = uniqueCategories.map(name => ({ name, slug: name.toLowerCase().replace(/\s+/g, '-') }));
            }
        }

        function showLoadingState() {
            document.getElementById('products-loading').classList.remove('hidden');
            document.getElementById('products-error').classList.add('hidden');
            document.getElementById('products-empty').classList.add('hidden');
            document.getElementById('products-grid').classList.add('hidden');
            document.getElementById('products-list').classList.add('hidden');
        }

        function hideLoadingState() {
            document.getElementById('products-loading').classList.add('hidden');
        }

        function showErrorState() {
            document.getElementById('products-loading').classList.add('hidden');
            document.getElementById('products-error').classList.remove('hidden');
            document.getElementById('products-grid').classList.add('hidden');
            document.getElementById('products-list').classList.add('hidden');
        }

        function showEmptyState() {
            document.getElementById('products-loading').classList.add('hidden');
            document.getElementById('products-error').classList.add('hidden');
            document.getElementById('products-empty').classList.remove('hidden');
            document.getElementById('products-grid').classList.add('hidden');
            document.getElementById('products-list').classList.add('hidden');
        }

        function populateCategories() {
            const categoryList = document.getElementById('category-list');
            const countAll = document.getElementById('count-all');

            // Update all products count
            countAll.textContent = products.length;

            // Clear existing categories
            categoryList.innerHTML = '';

            // Add categories
            categories.forEach(category => {
                const count = products.filter(p => p.category === category.name).length;

                const categoryButton = document.createElement('button');
                categoryButton.className = 'category-filter w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors';
                categoryButton.setAttribute('data-category', category.name);
                categoryButton.onclick = () => filterByCategory(category.name);

                categoryButton.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span class="text-gray-700">${category.name}</span>
                        <span class="text-sm text-gray-500">${count}</span>
                    </div>
                `;

                categoryList.appendChild(categoryButton);
            });
        }

        // Filtering Functions
        function filterByCategory(category) {
            currentFilters.category = category;

            // Update active category button
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-category') === category) {
                    btn.classList.add('active');
                }
            });

            applyFiltersAndSort();
        }

        function filterByPrice(min, max) {
            currentFilters.priceMin = min;
            currentFilters.priceMax = max;

            // Update active price button
            document.querySelectorAll('.price-filter').forEach(btn => {
                btn.classList.remove('bg-primary-100', 'text-primary-600');
                btn.classList.add('hover:bg-gray-100');
            });

            event.target.classList.add('bg-primary-100', 'text-primary-600');
            event.target.classList.remove('hover:bg-gray-100');

            applyFiltersAndSort();
        }

        function applyFiltersAndSort() {
            // Start with all products
            filteredProducts = [...products];

            // Apply category filter
            if (currentFilters.category) {
                filteredProducts = filteredProducts.filter(p => p.category === currentFilters.category);
            }

            // Apply search filter
            if (currentFilters.search) {
                const searchTerm = currentFilters.search.toLowerCase();
                filteredProducts = filteredProducts.filter(p =>
                    p.name.toLowerCase().includes(searchTerm) ||
                    (p.description && p.description.toLowerCase().includes(searchTerm)) ||
                    (p.category && p.category.toLowerCase().includes(searchTerm))
                );
            }

            // Apply price filter
            filteredProducts = filteredProducts.filter(p =>
                p.price >= currentFilters.priceMin && p.price <= currentFilters.priceMax
            );

            // Apply stock filter
            if (currentFilters.inStockOnly) {
                filteredProducts = filteredProducts.filter(p => p.stock_quantity > 0);
            }

            // Apply featured filter
            if (currentFilters.featuredOnly) {
                filteredProducts = filteredProducts.filter(p => p.featured);
            }

            // Apply sorting
            applySorting();

            // Display results
            displayProducts();
        }

        function applySorting() {
            switch (currentSort) {
                case 'name':
                    filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
                    break;
                case 'price-low':
                    filteredProducts.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    filteredProducts.sort((a, b) => b.price - a.price);
                    break;
                case 'stock':
                    filteredProducts.sort((a, b) => b.stock_quantity - a.stock_quantity);
                    break;
                case 'featured':
                    filteredProducts.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
                    break;
            }
        }

        function clearFilters() {
            currentFilters = {
                category: '',
                search: '',
                priceMin: 0,
                priceMax: 999,
                inStockOnly: false,
                featuredOnly: false
            };

            // Reset UI
            document.getElementById('search-input').value = '';
            document.getElementById('in-stock-filter').checked = false;
            document.getElementById('featured-filter').checked = false;

            // Reset category buttons
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('.category-filter[data-category=""]').classList.add('active');

            // Reset price buttons
            document.querySelectorAll('.price-filter').forEach(btn => {
                btn.classList.remove('bg-primary-100', 'text-primary-600');
                btn.classList.add('hover:bg-gray-100');
            });

            applyFiltersAndSort();
        }

        // Enhanced Product Display Functions
        function displayProducts() {
            const gridContainer = document.getElementById('products-grid');
            const listContainer = document.getElementById('products-list');
            const countElement = document.getElementById('products-count');

            // Update count
            countElement.textContent = `Showing ${filteredProducts.length} of ${products.length} products`;

            // Show/hide containers based on view mode
            if (currentViewMode === 'grid') {
                gridContainer.classList.remove('hidden');
                listContainer.classList.add('hidden');
                displayProductsGrid();
            } else {
                gridContainer.classList.add('hidden');
                listContainer.classList.remove('hidden');
                displayProductsList();
            }

            // Show empty state if no products
            if (filteredProducts.length === 0) {
                showEmptyState();
                return;
            }

            // Hide other states
            document.getElementById('products-loading').classList.add('hidden');
            document.getElementById('products-error').classList.add('hidden');
            document.getElementById('products-empty').classList.add('hidden');
        }

        function displayProductsGrid() {
            const container = document.getElementById('products-grid');

            container.innerHTML = filteredProducts.map(product => `
                <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200">
                    <!-- Product Image -->
                    <div class="relative">
                        <img src="${product.image_url || 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop'}"
                             alt="${product.name}"
                             class="w-full h-48 object-cover rounded-t-lg"
                             onerror="this.src='https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop'">

                        ${product.featured ? '<div class="absolute top-2 left-2 bg-primary-600 text-white text-xs px-2 py-1 rounded">Featured</div>' : ''}
                        ${product.stock_quantity === 0 ? '<div class="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded">Out of Stock</div>' : ''}
                        ${product.stock_quantity > 0 && product.stock_quantity <= 10 ? '<div class="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded">Low Stock</div>' : ''}
                    </div>

                    <!-- Product Info -->
                    <div class="p-4">
                        <!-- Category -->
                        <div class="text-xs text-primary-600 font-medium mb-1">${product.category || 'Medical Supplies'}</div>

                        <!-- Product Name -->
                        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">${product.name}</h3>

                        <!-- Description -->
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">${product.description || 'Professional medical supply'}</p>

                        <!-- Packaging Info -->
                        <div class="text-sm text-gray-500 mb-3">
                            <span class="inline-flex items-center">
                                📦 ${getPackagingInfo(product)}
                            </span>
                        </div>

                        <!-- Pricing -->
                        <div class="mb-4">
                            <div class="flex items-center space-x-2">
                                <span class="text-lg font-bold text-gray-900">$${product.price.toFixed(2)}</span>
                                ${product.bulk_price && product.bulk_price < product.price ?
                                    `<span class="text-sm text-green-600">Bulk: $${product.bulk_price.toFixed(2)}</span>` : ''}
                            </div>
                            ${product.bulk_price && product.bulk_price < product.price ?
                                `<div class="text-xs text-gray-500">Save $${(product.price - product.bulk_price).toFixed(2)} on bulk orders</div>` : ''}
                        </div>

                        <!-- Stock Status -->
                        <div class="mb-4">
                            ${product.stock_quantity > 0 ?
                                `<div class="flex items-center text-sm text-green-600">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    In Stock (${product.stock_quantity} available)
                                </div>` :
                                `<div class="flex items-center text-sm text-red-600">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    Out of Stock
                                </div>`
                            }
                        </div>

                        <!-- Add to Cart Section -->
                        <div class="space-y-3">
                            ${product.stock_quantity > 0 ? `
                                <div class="flex items-center space-x-2">
                                    <div class="flex items-center border rounded-md">
                                        <button onclick="decreaseQuantity(${product.id})" class="px-3 py-1 hover:bg-gray-100 text-gray-600">-</button>
                                        <input type="number" id="qty-${product.id}" value="1" min="1" max="${product.stock_quantity}"
                                               class="w-16 text-center border-0 focus:ring-0 text-sm">
                                        <button onclick="increaseQuantity(${product.id})" class="px-3 py-1 hover:bg-gray-100 text-gray-600">+</button>
                                    </div>
                                    <button onclick="addToCartFromGrid(${product.id})"
                                            class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors text-sm">
                                        Add to Cart
                                    </button>
                                </div>
                            ` : `
                                <button disabled class="w-full bg-gray-300 text-gray-500 px-4 py-2 rounded-md font-medium text-sm cursor-not-allowed">
                                    Out of Stock
                                </button>
                            `}

                            <button onclick="addToWishlist(${product.id})"
                                    class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-md font-medium transition-colors text-sm">
                                ♡ Add to Wishlist
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getPackagingInfo(product) {
            // Extract packaging info from product name or use defaults
            if (product.name.includes('Box of')) {
                return product.name.match(/Box of \d+/)[0];
            } else if (product.name.includes('Pack of')) {
                return product.name.match(/Pack of \d+/)[0];
            } else if (product.name.includes('ml') || product.name.includes('oz')) {
                const match = product.name.match(/\d+\s*(ml|oz)/);
                return match ? match[0] : 'Individual Unit';
            } else {
                return 'Individual Unit';
            }
        }

        // Utility Functions for Product Catalog
        function addToCartFromGrid(productId) {
            const quantity = parseInt(document.getElementById(`qty-${productId}`).value) || 1;
            addToCart(productId, quantity);
        }

        function increaseQuantity(productId) {
            const input = document.getElementById(`qty-${productId}`);
            const max = parseInt(input.getAttribute('max'));
            const current = parseInt(input.value);
            if (current < max) {
                input.value = current + 1;
            }
        }

        function decreaseQuantity(productId) {
            const input = document.getElementById(`qty-${productId}`);
            const current = parseInt(input.value);
            if (current > 1) {
                input.value = current - 1;
            }
        }

        function setViewMode(mode) {
            currentViewMode = mode;

            // Update view buttons
            const gridBtn = document.getElementById('grid-view-btn');
            const listBtn = document.getElementById('list-view-btn');

            if (mode === 'grid') {
                gridBtn.classList.add('bg-primary-100', 'text-primary-600');
                gridBtn.classList.remove('text-gray-400');
                listBtn.classList.remove('bg-primary-100', 'text-primary-600');
                listBtn.classList.add('text-gray-400');
            } else {
                listBtn.classList.add('bg-primary-100', 'text-primary-600');
                listBtn.classList.remove('text-gray-400');
                gridBtn.classList.remove('bg-primary-100', 'text-primary-600');
                gridBtn.classList.add('text-gray-400');
            }

            displayProducts();
        }

        function addToWishlist(productId) {
            // Placeholder for wishlist functionality
            showToast('Added to wishlist!', 'success');
        }

        // Event Listeners for Product Catalog
        document.addEventListener('DOMContentLoaded', function() {
            // Search input
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.addEventListener('input', function(e) {
                    currentFilters.search = e.target.value;
                    applyFiltersAndSort();
                });
            }

            // Sort select
            const sortSelect = document.getElementById('sort-select');
            if (sortSelect) {
                sortSelect.addEventListener('change', function(e) {
                    currentSort = e.target.value;
                    applyFiltersAndSort();
                });
            }

            // Filter checkboxes
            const inStockFilter = document.getElementById('in-stock-filter');
            if (inStockFilter) {
                inStockFilter.addEventListener('change', function(e) {
                    currentFilters.inStockOnly = e.target.checked;
                    applyFiltersAndSort();
                });
            }

            const featuredFilter = document.getElementById('featured-filter');
            if (featuredFilter) {
                featuredFilter.addEventListener('change', function(e) {
                    currentFilters.featuredOnly = e.target.checked;
                    applyFiltersAndSort();
                });
            }
        });

        // Load orders
        async function loadOrders() {
            const loadingEl = document.getElementById('orders-loading');
            const emptyEl = document.getElementById('orders-empty');
            const listEl = document.getElementById('orders-list');

            // Show loading
            loadingEl.classList.remove('hidden');
            emptyEl.classList.add('hidden');
            listEl.innerHTML = '';

            try {
                const response = await fetch('/api/orders');
                const data = await response.json();

                loadingEl.classList.add('hidden');

                if (response.ok) {
                    if (data.orders && data.orders.length > 0) {
                        displayOrders(data.orders);
                    } else {
                        emptyEl.classList.remove('hidden');
                    }
                } else {
                    showToast('Failed to load orders: ' + data.error, 'error');
                    emptyEl.classList.remove('hidden');
                }
            } catch (error) {
                loadingEl.classList.add('hidden');
                showToast('Error loading orders: ' + error.message, 'error');
                emptyEl.classList.remove('hidden');
            }
        }

        // Display orders with enhanced status management
        function displayOrders(orders) {
            const listEl = document.getElementById('orders-list');

            listEl.innerHTML = orders.map(order => {
                const statusColor = getStatusColor(order.status);
                const statusIcon = getStatusIcon(order.status);
                const progressPercentage = getStatusProgress(order.status);

                return `
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Order #${order.order_number}</h3>
                                <p class="text-sm text-gray-600">Placed on ${new Date(order.created_at).toLocaleDateString()}</p>
                                ${order.status_updated_at ? `
                                    <p class="text-xs text-gray-500">Status updated: ${new Date(order.status_updated_at).toLocaleString()}</p>
                                ` : ''}
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="${statusColor} px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                                    ${statusIcon} ${order.status}
                                </span>
                            </div>
                        </div>

                        <!-- Enhanced Status Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium text-gray-700">Order Progress</h4>
                                <span class="text-sm text-gray-600">${progressPercentage}% Complete</span>
                            </div>
                            ${generateStatusTimeline(order.status)}
                        </div>

                        <!-- Tracking Information -->
                        ${order.tracking_number ? `
                            <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-1">Tracking Information</h4>
                                <p class="text-sm text-blue-700">Tracking Number: <span class="font-mono">${order.tracking_number}</span></p>
                                ${order.estimated_delivery ? `
                                    <p class="text-sm text-blue-700">Estimated Delivery: ${new Date(order.estimated_delivery).toLocaleDateString()}</p>
                                ` : ''}
                            </div>
                        ` : ''}

                        <div class="grid md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <h4 class="font-medium text-gray-700 mb-2">Shipping Address</h4>
                                <p class="text-sm text-gray-600">${order.shipping_address}</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-700 mb-2">Order Summary</h4>
                                <p class="text-sm text-gray-600">${order.item_count} item(s)</p>
                                <p class="text-lg font-bold text-primary-600">$${order.total_amount}</p>
                            </div>
                        </div>

                        ${order.notes ? `
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-700 mb-2">Notes</h4>
                                <p class="text-sm text-gray-600">${order.notes}</p>
                            </div>
                        ` : ''}

                        <div class="flex flex-wrap gap-2">
                            <button onclick="viewOrderDetails(${order.id})"
                                    class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm">
                                📋 View Details
                            </button>
                            <button onclick="viewInvoice(${order.id})"
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                                📄 Invoice
                            </button>
                            <button onclick="viewOrderStatusHistory(${order.id})"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                📈 Status History
                            </button>
                            ${order.status === 'Pending' ? `
                                <button onclick="cancelOrder(${order.id})"
                                        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                                    ❌ Cancel Order
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getStatusColor(status) {
            switch (status) {
                case 'Pending': return 'bg-gray-100 text-gray-800';
                case 'Processing': return 'bg-blue-100 text-blue-800';
                case 'Shipped': return 'bg-green-100 text-green-800';
                case 'Complete': return 'bg-green-200 text-green-900';
                case 'Cancelled': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusIcon(status) {
            switch (status) {
                case 'Pending': return '⏳';
                case 'Processing': return '⚙️';
                case 'Shipped': return '🚚';
                case 'Complete': return '✅';
                case 'Cancelled': return '❌';
                default: return '📋';
            }
        }

        function getStatusProgress(status) {
            switch (status) {
                case 'Pending': return 25;
                case 'Processing': return 50;
                case 'Shipped': return 75;
                case 'Complete': return 100;
                case 'Cancelled': return 0;
                default: return 0;
            }
        }

        function generateStatusTimeline(currentStatus) {
            const statuses = ['Pending', 'Processing', 'Shipped', 'Complete'];
            const currentIndex = statuses.indexOf(currentStatus);

            let timeline = '<div class="flex items-center justify-between mb-2">';

            statuses.forEach((status, index) => {
                const isActive = index <= currentIndex;
                const isCurrentStatus = status === currentStatus;
                const color = isActive ? getStatusColor(status).split(' ')[0].replace('bg-', '') : 'gray-300';

                timeline += `
                    <div class="flex flex-col items-center flex-1">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${isActive ? getStatusColor(status) : 'bg-gray-300 text-gray-600'} ${isCurrentStatus ? 'ring-2 ring-offset-2 ring-blue-500' : ''}">
                            ${getStatusIcon(status)}
                        </div>
                        <span class="text-xs mt-1 ${isActive ? 'text-gray-800 font-medium' : 'text-gray-500'}">${status}</span>
                    </div>
                `;

                if (index < statuses.length - 1) {
                    timeline += `<div class="flex-1 h-1 mx-2 ${isActive && index < currentIndex ? 'bg-green-400' : 'bg-gray-300'} rounded"></div>`;
                }
            });

            timeline += '</div>';
            return timeline;
        }

        async function viewOrderDetails(orderId) {
            try {
                const response = await fetch(`/api/orders/${orderId}`);
                const order = await response.json();

                if (response.ok) {
                    showOrderDetailsModal(order);
                } else {
                    showToast('Failed to load order details: ' + order.error, 'error');
                }
            } catch (error) {
                showToast('Error loading order details: ' + error.message, 'error');
            }
        }

        function showOrderDetailsModal(order) {
            // For now, just show an alert with order details
            // In a full implementation, this would show a detailed modal
            const itemsList = order.items.map(item =>
                `${item.quantity}x ${item.product_name} - $${item.total_price}`
            ).join('\\n');

            alert(`Order Details:\\n\\nOrder #${order.order_number}\\nStatus: ${order.status}\\nTotal: $${order.total_amount}\\n\\nItems:\\n${itemsList}`);
        }

        function viewInvoice(orderId) {
            // Open invoice in new window
            const invoiceUrl = `/api/orders/${orderId}/invoice`;
            window.open(invoiceUrl, '_blank', 'width=800,height=600,scrollbars=yes');
        }

        async function cancelOrder(orderId) {
            if (!confirm('Are you sure you want to cancel this order?')) {
                return;
            }

            // For now, just show a message
            // In a full implementation, this would call an API to cancel the order
            showToast('Order cancellation functionality will be implemented in admin panel', 'info');
        }

        // New enhanced order status functions
        async function viewOrderStatusHistory(orderId) {
            try {
                const response = await fetch(`/api/orders/${orderId}/status-history`);
                const data = await response.json();

                if (response.ok) {
                    showOrderStatusHistoryModal(orderId, data.history);
                } else {
                    showToast('Failed to load order status history: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('Error loading order status history: ' + error.message, 'error');
            }
        }

        function showOrderStatusHistoryModal(orderId, history) {
            let historyHtml = `Order #${orderId} Status History:\\n\\n`;

            if (history.length === 0) {
                historyHtml += 'No status changes recorded.';
            } else {
                history.forEach(change => {
                    const date = new Date(change.created_at).toLocaleString();
                    historyHtml += `${date}\\n`;
                    historyHtml += `${change.old_status || 'New'} → ${change.new_status}\\n`;
                    historyHtml += `Changed by: ${change.changed_by}\\n`;
                    if (change.change_reason) {
                        historyHtml += `Reason: ${change.change_reason}\\n`;
                    }
                    historyHtml += '\\n';
                });
            }

            alert(historyHtml);
        }

        async function refreshOrders() {
            showToast('Refreshing orders...', 'info');
            await loadOrders();
            showToast('Orders refreshed successfully!', 'success');
        }

        // Notifications Functions
        let allNotifications = [];
        let currentFilter = 'all';

        async function loadNotifications() {
            const loadingEl = document.getElementById('notifications-loading');
            const emptyEl = document.getElementById('notifications-empty');
            const listEl = document.getElementById('notifications-list');

            // Show loading
            loadingEl.classList.remove('hidden');
            emptyEl.classList.add('hidden');
            listEl.innerHTML = '';

            try {
                // For now, create mock notifications based on orders
                const mockNotifications = await generateMockNotifications();

                loadingEl.classList.add('hidden');

                if (mockNotifications.length > 0) {
                    allNotifications = mockNotifications;
                    displayNotifications(allNotifications);
                    updateNotificationBadge();
                } else {
                    emptyEl.classList.remove('hidden');
                }
            } catch (error) {
                loadingEl.classList.add('hidden');
                showToast('Error loading notifications: ' + error.message, 'error');
                emptyEl.classList.remove('hidden');
            }
        }

        async function generateMockNotifications() {
            // Generate dynamic notifications including email notifications
            const notifications = [
                {
                    id: 1,
                    type: 'email',
                    title: '📧 New Email Notification',
                    message: 'You have received a new email regarding your recent order inquiry. Please check your email inbox.',
                    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
                    read: false,
                    icon: '📧',
                    color: 'bg-red-100 text-red-800'
                },
                {
                    id: 2,
                    type: 'email',
                    title: '📬 Order Status Update Email',
                    message: 'Email notification: Your order status has been updated. Check your email for detailed tracking information.',
                    timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
                    read: false,
                    icon: '📬',
                    color: 'bg-orange-100 text-orange-800'
                },
                {
                    id: 3,
                    type: 'order',
                    title: 'Order Confirmation',
                    message: 'Your order #GM20250627A1B2C3 has been confirmed and is being processed.',
                    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                    read: false,
                    icon: '📦',
                    color: 'bg-blue-100 text-blue-800'
                },
                {
                    id: 4,
                    type: 'email',
                    title: '💌 Welcome Email Sent',
                    message: 'Welcome email has been sent to your registered email address with account setup instructions.',
                    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
                    read: true,
                    icon: '💌',
                    color: 'bg-pink-100 text-pink-800'
                },
                {
                    id: 5,
                    type: 'shipping',
                    title: 'Order Shipped',
                    message: 'Your order #**************** has been shipped and is on its way to you.',
                    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
                    read: true,
                    icon: '🚚',
                    color: 'bg-purple-100 text-purple-800'
                },
                {
                    id: 6,
                    type: 'email',
                    title: '📨 Shipping Confirmation Email',
                    message: 'Email notification: Shipping confirmation with tracking details has been sent to your email.',
                    timestamp: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 25 hours ago
                    read: true,
                    icon: '📨',
                    color: 'bg-indigo-100 text-indigo-800'
                },
                {
                    id: 7,
                    type: 'order',
                    title: 'Order Delivered',
                    message: 'Your order #GM20250624M5N6P7 has been successfully delivered.',
                    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
                    read: true,
                    icon: '✅',
                    color: 'bg-green-100 text-green-800'
                },
                {
                    id: 8,
                    type: 'system',
                    title: 'Welcome to Grace Medical',
                    message: 'Thank you for registering with Grace Medical Limited. Explore our wide range of medical supplies.',
                    timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
                    read: true,
                    icon: '🏥',
                    color: 'bg-gray-100 text-gray-800'
                }
            ];

            return notifications;
        }

        // Function to simulate new email notifications arriving
        function simulateNewEmailNotifications() {
            const emailNotifications = [
                {
                    type: 'email',
                    title: '📧 New Email: Order Inquiry Response',
                    message: 'You have received a response to your product inquiry. Please check your email for details.',
                    icon: '📧',
                    color: 'bg-red-100 text-red-800'
                },
                {
                    type: 'email',
                    title: '📬 Email: Payment Confirmation',
                    message: 'Payment confirmation email has been sent to your registered email address.',
                    icon: '📬',
                    color: 'bg-green-100 text-green-800'
                },
                {
                    type: 'email',
                    title: '💌 Email: Special Offer',
                    message: 'Check your email for exclusive offers on medical supplies this month.',
                    icon: '💌',
                    color: 'bg-purple-100 text-purple-800'
                },
                {
                    type: 'email',
                    title: '📨 Email: Account Security',
                    message: 'Important security update email has been sent to your email address.',
                    icon: '📨',
                    color: 'bg-orange-100 text-orange-800'
                }
            ];

            // Add a random email notification every 30 seconds when on notifications page
            if (!document.getElementById('notifications-section').classList.contains('hidden')) {
                const randomNotification = emailNotifications[Math.floor(Math.random() * emailNotifications.length)];
                const newNotification = {
                    id: Date.now(),
                    ...randomNotification,
                    timestamp: new Date().toISOString(),
                    read: false
                };

                // Add to the beginning of notifications array
                allNotifications.unshift(newNotification);

                // Refresh the display
                displayNotifications(allNotifications);
                updateNotificationBadge();

                // Show toast notification
                showToast('📧 New email notification received!', 'info');
            }
        }

        // Start simulating email notifications every 30 seconds
        setInterval(simulateNewEmailNotifications, 30000);

        function displayNotifications(notifications) {
            const listEl = document.getElementById('notifications-list');

            if (notifications.length === 0) {
                listEl.innerHTML = '<div class="text-center py-8 text-gray-500">No notifications found for this filter.</div>';
                return;
            }

            listEl.innerHTML = notifications.map(notification => {
                const timeAgo = getTimeAgo(new Date(notification.timestamp));

                return `
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow ${notification.read ? '' : 'border-l-4 border-l-primary-500'}">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 ${notification.color} rounded-full flex items-center justify-center text-lg">
                                    ${notification.icon}
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-sm font-semibold text-gray-900 ${notification.read ? '' : 'font-bold'}">
                                        ${notification.title}
                                    </h4>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs text-gray-500">${timeAgo}</span>
                                        ${!notification.read ? '<div class="w-2 h-2 bg-primary-500 rounded-full"></div>' : ''}
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">${notification.message}</p>
                                <div class="flex items-center justify-between mt-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${notification.color}">
                                        ${notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                                    </span>
                                    <div class="flex space-x-2">
                                        ${!notification.read ? `
                                            <button onclick="markNotificationRead(${notification.id})" class="text-xs text-primary-600 hover:text-primary-700 font-medium">
                                                Mark as Read
                                            </button>
                                        ` : ''}
                                        <button onclick="viewNotificationDetails(${notification.id})" class="text-xs text-gray-600 hover:text-gray-700 font-medium">
                                            View Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function filterNotifications(type) {
            currentFilter = type;

            // Update filter buttons
            document.querySelectorAll('.notification-filter').forEach(btn => {
                btn.classList.remove('active', 'bg-primary-600', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700');
            });

            document.getElementById(`filter-${type}`).classList.remove('bg-gray-200', 'text-gray-700');
            document.getElementById(`filter-${type}`).classList.add('active', 'bg-primary-600', 'text-white');

            // Filter notifications
            let filteredNotifications = allNotifications;
            if (type !== 'all') {
                filteredNotifications = allNotifications.filter(n => n.type === type);
            }

            displayNotifications(filteredNotifications);
        }

        function updateNotificationBadge() {
            const unreadCount = allNotifications.filter(n => !n.read).length;
            const badge = document.getElementById('notification-badge');
            const mainBadge = document.getElementById('main-notification-badge');
            const unreadCountEl = document.getElementById('unread-count');

            // Update both badges
            [badge, mainBadge].forEach(badgeEl => {
                if (badgeEl) {
                    if (unreadCount > 0) {
                        badgeEl.textContent = unreadCount;
                        badgeEl.classList.remove('hidden');
                    } else {
                        badgeEl.classList.add('hidden');
                    }
                }
            });

            if (unreadCountEl) {
                unreadCountEl.textContent = unreadCount;
            }
        }

        function markNotificationRead(notificationId) {
            const notification = allNotifications.find(n => n.id === notificationId);
            if (notification) {
                notification.read = true;
                displayNotifications(currentFilter === 'all' ? allNotifications : allNotifications.filter(n => n.type === currentFilter));
                updateNotificationBadge();
                showToast('Notification marked as read', 'success');
            }
        }

        function markAllNotificationsRead() {
            allNotifications.forEach(n => n.read = true);
            displayNotifications(currentFilter === 'all' ? allNotifications : allNotifications.filter(n => n.type === currentFilter));
            updateNotificationBadge();
            showToast('All notifications marked as read', 'success');
        }

        function viewNotificationDetails(notificationId) {
            const notification = allNotifications.find(n => n.id === notificationId);
            if (notification) {
                showEmailDetailsModal(notification);
            }
        }

        function showEmailDetailsModal(notification) {
            // Create modal HTML
            const modalHtml = `
                <div id="email-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div class="p-6">
                            <!-- Modal Header -->
                            <div class="flex justify-between items-start mb-6">
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">${notification.icon} ${notification.title}</h3>
                                    <p class="text-sm text-gray-500 mt-1">
                                        ${notification.type.charAt(0).toUpperCase() + notification.type.slice(1)} •
                                        ${new Date(notification.timestamp).toLocaleString()}
                                    </p>
                                </div>
                                <button onclick="closeEmailDetailsModal()" class="text-gray-400 hover:text-gray-600">
                                    <span class="text-2xl">&times;</span>
                                </button>
                            </div>

                            <!-- Email Content -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Subject:</label>
                                        <p class="text-gray-900 font-medium">${notification.title}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Message:</label>
                                        <div class="bg-white rounded border p-4">
                                            <p class="text-gray-800 leading-relaxed">${notification.message}</p>
                                        </div>
                                    </div>

                                    ${notification.type === 'email' ? `
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Details:</label>
                                            <div class="bg-white rounded border p-4 space-y-2">
                                                <p><strong>From:</strong> Grace Medical Limited &lt;<EMAIL>&gt;</p>
                                                <p><strong>To:</strong> ${getCurrentUserEmail()}</p>
                                                <p><strong>Date:</strong> ${new Date(notification.timestamp).toLocaleString()}</p>
                                                <p><strong>Priority:</strong> ${notification.read ? 'Normal' : 'High'}</p>
                                            </div>
                                        </div>
                                    ` : ''}

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Status:</label>
                                        <span class="${notification.read ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} px-3 py-1 rounded-full text-sm font-medium">
                                            ${notification.read ? '✓ Read' : '● Unread'}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-end space-x-3">
                                ${!notification.read ? `
                                    <button onclick="markNotificationReadFromModal(${notification.id})" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                                        Mark as Read
                                    </button>
                                ` : ''}
                                <button onclick="closeEmailDetailsModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg font-medium">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeEmailDetailsModal() {
            const modal = document.getElementById('email-details-modal');
            if (modal) {
                modal.remove();
            }
        }

        function markNotificationReadFromModal(notificationId) {
            markNotificationRead(notificationId);
            closeEmailDetailsModal();
        }

        function getCurrentUserEmail() {
            // Get current user email from session or default
            return currentUser?.email || '<EMAIL>';
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
            return date.toLocaleDateString();
        }

        // Admin Functions
        function showAdminTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.admin-tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.admin-tab').forEach(tab => {
                tab.classList.remove('border-purple-500', 'text-purple-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(`admin-${tabName}`).classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(`tab-${tabName}`);
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-purple-500', 'text-purple-600');

            // Load data based on tab
            switch(tabName) {
                case 'overview':
                    loadAdminOverview();
                    break;
                case 'products':
                    loadAdminProducts();
                    break;
                case 'orders':
                    loadAdminOrders();
                    break;
                case 'stock':
                    loadStockAlerts();
                    break;
            }
        }

        async function loadAdminOverview() {
            try {
                // Load statistics
                const [productsRes, ordersRes] = await Promise.all([
                    fetch('/api/products'),
                    fetch('/api/orders')
                ]);

                const productsData = await productsRes.json();
                const ordersData = await ordersRes.json();

                if (productsRes.ok && ordersRes.ok) {
                    const products = productsData.products || [];
                    const orders = ordersData.orders || [];

                    // Update statistics
                    document.getElementById('stat-products').textContent = products.length;
                    document.getElementById('stat-orders').textContent = orders.length;

                    const lowStockCount = products.filter(p => p.is_low_stock).length;
                    document.getElementById('stat-low-stock').textContent = lowStockCount;

                    const totalRevenue = orders.reduce((sum, order) => sum + order.total_amount, 0);
                    document.getElementById('stat-revenue').textContent = `$${totalRevenue.toFixed(2)}`;

                    // Show recent orders
                    const recentOrders = orders.slice(0, 5);
                    displayRecentOrders(recentOrders);
                }
            } catch (error) {
                showToast('Error loading admin overview: ' + error.message, 'error');
            }
        }

        function displayRecentOrders(orders) {
            const container = document.getElementById('admin-recent-orders');

            if (orders.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No recent orders</p>';
                return;
            }

            container.innerHTML = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${orders.map(order => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${order.order_number}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(order.created_at).toLocaleDateString()}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="${getStatusColor(order.status)} px-2 py-1 rounded-full text-xs font-medium">
                                            ${order.status}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${order.total_amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="updateOrderStatus(${order.id})" class="text-purple-600 hover:text-purple-900">Update Status</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // Global variable to store all products for filtering/sorting
        let allAdminProducts = [];

        async function loadAdminProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();

                if (response.ok) {
                    allAdminProducts = data.products || [];
                    populateCategoryFilter();
                    displayAdminProducts(allAdminProducts);
                } else {
                    showToast('Failed to load products: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('Error loading products: ' + error.message, 'error');
            }
        }

        function populateCategoryFilter() {
            const categoryFilter = document.getElementById('category-filter');
            const categories = [...new Set(allAdminProducts.map(p => p.category).filter(c => c))];

            // Clear existing options except "All Categories"
            categoryFilter.innerHTML = '<option value="">All Categories</option>';

            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categoryFilter.appendChild(option);
            });
        }

        function filterProducts() {
            const searchTerm = document.getElementById('product-search').value.toLowerCase();
            const categoryFilter = document.getElementById('category-filter').value;
            const stockFilter = document.getElementById('stock-filter').value;

            let filteredProducts = allAdminProducts.filter(product => {
                // Search filter
                const matchesSearch = !searchTerm ||
                    product.name.toLowerCase().includes(searchTerm) ||
                    (product.category && product.category.toLowerCase().includes(searchTerm)) ||
                    (product.manufacturer && product.manufacturer.toLowerCase().includes(searchTerm));

                // Category filter
                const matchesCategory = !categoryFilter || product.category === categoryFilter;

                // Stock filter
                let matchesStock = true;
                if (stockFilter === 'low') {
                    matchesStock = product.is_low_stock;
                } else if (stockFilter === 'normal') {
                    matchesStock = !product.is_low_stock && product.stock_quantity > 0;
                } else if (stockFilter === 'out') {
                    matchesStock = product.stock_quantity === 0;
                }

                return matchesSearch && matchesCategory && matchesStock;
            });

            displayAdminProducts(filteredProducts);
        }

        function sortProducts() {
            const sortBy = document.getElementById('sort-products').value;
            const searchTerm = document.getElementById('product-search').value.toLowerCase();
            const categoryFilter = document.getElementById('category-filter').value;
            const stockFilter = document.getElementById('stock-filter').value;

            // First apply filters
            let filteredProducts = allAdminProducts.filter(product => {
                const matchesSearch = !searchTerm ||
                    product.name.toLowerCase().includes(searchTerm) ||
                    (product.category && product.category.toLowerCase().includes(searchTerm)) ||
                    (product.manufacturer && product.manufacturer.toLowerCase().includes(searchTerm));

                const matchesCategory = !categoryFilter || product.category === categoryFilter;

                let matchesStock = true;
                if (stockFilter === 'low') {
                    matchesStock = product.is_low_stock;
                } else if (stockFilter === 'normal') {
                    matchesStock = !product.is_low_stock && product.stock_quantity > 0;
                } else if (stockFilter === 'out') {
                    matchesStock = product.stock_quantity === 0;
                }

                return matchesSearch && matchesCategory && matchesStock;
            });

            // Then sort
            filteredProducts.sort((a, b) => {
                switch(sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'name-desc':
                        return b.name.localeCompare(a.name);
                    case 'price':
                        return a.unit_price - b.unit_price;
                    case 'price-desc':
                        return b.unit_price - a.unit_price;
                    case 'stock':
                        return a.stock_quantity - b.stock_quantity;
                    case 'stock-desc':
                        return b.stock_quantity - a.stock_quantity;
                    default:
                        return 0;
                }
            });

            displayAdminProducts(filteredProducts);
        }

        function displayAdminProducts(products) {
            const container = document.getElementById('admin-products-list');

            if (products.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No products found</p>';
                return;
            }

            container.innerHTML = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${products.map(product => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="${product.image_url || 'https://via.placeholder.com/40x40/e5e7eb/6b7280?text=Med'}"
                                                 alt="${product.name}" class="w-10 h-10 rounded object-cover">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">${product.name}</div>
                                                <div class="text-sm text-gray-500">${product.manufacturer || 'N/A'}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${product.category || 'General'}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${product.unit_price}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="${product.is_low_stock ? 'text-red-600' : 'text-green-600'} text-sm font-medium">
                                            ${product.stock_quantity}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="${product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} px-2 py-1 rounded-full text-xs font-medium">
                                            ${product.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="editProduct(${product.id})" class="text-purple-600 hover:text-purple-900 mr-3">Edit</button>
                                        <button onclick="updateStock(${product.id})" class="text-blue-600 hover:text-blue-900">Update Stock</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        async function loadAdminOrders() {
            try {
                const response = await fetch('/api/orders');
                const data = await response.json();

                if (response.ok) {
                    displayAdminOrders(data.orders || []);
                } else {
                    showToast('Failed to load orders: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('Error loading orders: ' + error.message, 'error');
            }
        }

        function displayAdminOrders(orders) {
            const container = document.getElementById('admin-orders-list');

            if (orders.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No orders found</p>';
                return;
            }

            container.innerHTML = `
                <div class="mb-4 flex justify-between items-center">
                    <h4 class="text-lg font-semibold">Order Management</h4>
                    <div class="flex gap-2">
                        <button onclick="loadAdminOrders()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                            🔄 Refresh Orders
                        </button>
                        <button onclick="runAutoStatusUpdates()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm">
                            ⚡ Run Auto Updates
                        </button>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${orders.map(order => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">#${order.order_number}</div>
                                        <div class="text-sm text-gray-500">${order.item_count} items</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">${order.user_email || 'N/A'}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="${getStatusColor(order.status)} px-2 py-1 rounded-full text-xs font-medium">
                                            ${getStatusIcon(order.status)} ${order.status}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        $${order.total_amount}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${new Date(order.created_at).toLocaleDateString()}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="showUpdateStatusModal(${order.id}, '${order.status}')"
                                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs">
                                            Update Status
                                        </button>
                                        <button onclick="viewOrderDetails(${order.id})"
                                                class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs">
                                            View Details
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        async function loadStockAlerts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();

                if (response.ok) {
                    const lowStockProducts = (data.products || []).filter(p => p.is_low_stock);
                    displayStockAlerts(lowStockProducts);
                } else {
                    showToast('Failed to load stock alerts: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('Error loading stock alerts: ' + error.message, 'error');
            }
        }

        function displayStockAlerts(products) {
            const container = document.getElementById('admin-stock-alerts');

            if (products.length === 0) {
                container.innerHTML = '<p class="text-green-600">No low stock alerts! All products are well stocked.</p>';
                return;
            }

            container.innerHTML = `
                <div class="space-y-4">
                    ${products.map(product => `
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">${product.name}</h4>
                                        <p class="text-sm text-gray-600">Current stock: ${product.stock_quantity} | Reorder level: ${product.reorder_level}</p>
                                    </div>
                                </div>
                                <button onclick="updateStock(${product.id})" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                    Restock
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Enhanced admin action functions
        function showUpdateStatusModal(orderId, currentStatus) {
            const statuses = ['Pending', 'Processing', 'Shipped', 'Complete', 'Cancelled'];
            const statusOptions = statuses.map(status =>
                `<option value="${status}" ${status === currentStatus ? 'selected' : ''}>${status}</option>`
            ).join('');

            const modalHtml = `
                <div id="status-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div class="mt-3">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Update Order Status</h3>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">New Status:</label>
                                <select id="new-status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    ${statusOptions}
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tracking Number (optional):</label>
                                <input type="text" id="tracking-number" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter tracking number">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Reason (optional):</label>
                                <textarea id="change-reason" class="w-full border border-gray-300 rounded-md px-3 py-2" rows="3" placeholder="Enter reason for status change"></textarea>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button onclick="closeStatusModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                                    Cancel
                                </button>
                                <button onclick="updateOrderStatus(${orderId})" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                                    Update Status
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeStatusModal() {
            const modal = document.getElementById('status-modal');
            if (modal) {
                modal.remove();
            }
        }

        async function updateOrderStatus(orderId) {
            const newStatus = document.getElementById('new-status').value;
            const trackingNumber = document.getElementById('tracking-number').value;
            const changeReason = document.getElementById('change-reason').value;

            if (!newStatus) {
                showToast('Please select a status', 'error');
                return;
            }

            try {
                // Get authentication headers
                const headers = {
                    'Content-Type': 'application/json',
                };

                // Add authentication token if available
                const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch(`/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: headers,
                    body: JSON.stringify({
                        status: newStatus,
                        tracking_number: trackingNumber || null,
                        reason: changeReason || null
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Order status updated successfully!', 'success');
                    closeStatusModal();
                    loadAdminOrders(); // Refresh the orders list
                } else {
                    showToast('Failed to update order status: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Error updating order status:', error);
                showToast('Error updating order status: ' + error.message, 'error');
            }
        }

        async function runAutoStatusUpdates() {
            try {
                // Get authentication headers
                const headers = {
                    'Content-Type': 'application/json',
                };

                // Add authentication token if available
                const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/api/admin/run-auto-status-updates', {
                    method: 'POST',
                    headers: headers
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Auto status updates completed successfully!', 'success');
                    loadAdminOrders(); // Refresh the orders list
                } else {
                    showToast('Failed to run auto status updates: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Error running auto status updates:', error);
                showToast('Error running auto status updates: ' + error.message, 'error');
            }
        }

        async function editProduct(productId) {
            try {
                // First, get the product details
                const response = await fetch(`/api/products/${productId}`);
                const product = await response.json();

                if (response.ok) {
                    showEditProductModal(product);
                } else {
                    showToast('Failed to load product details: ' + (product.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Error loading product:', error);
                showToast('Error loading product details: ' + error.message, 'error');
            }
        }

        function showEditProductModal(product) {
            const modalHtml = `
                <div id="edit-product-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div class="p-6">
                            <!-- Modal Header -->
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-xl font-bold text-gray-900">Edit Product</h3>
                                <button onclick="closeEditProductModal()" class="text-gray-400 hover:text-gray-600">
                                    <span class="text-2xl">&times;</span>
                                </button>
                            </div>

                            <!-- Edit Form -->
                            <form id="edit-product-form" class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                                        <input type="text" id="edit-product-name" value="${product.name || ''}"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                        <input type="text" id="edit-product-category" value="${product.category || ''}"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea id="edit-product-description" rows="3"
                                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">${product.description || ''}</textarea>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
                                        <input type="number" id="edit-product-price" value="${product.price || ''}" step="0.01" min="0"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Stock Quantity *</label>
                                        <input type="number" id="edit-product-stock" value="${product.stock_quantity || 0}" min="0"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Low Stock Alert</label>
                                        <input type="number" id="edit-product-low-stock" value="${product.low_stock_threshold || 10}" min="0"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Image URL</label>
                                    <input type="url" id="edit-product-image" value="${product.image_url || ''}"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="https://example.com/image.jpg">
                                </div>

                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="edit-product-active" ${product.is_active ? 'checked' : ''}
                                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Product is active</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input type="checkbox" id="edit-product-bulk" ${product.bulk_price ? 'checked' : ''}
                                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Bulk pricing available</span>
                                    </label>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex justify-end space-x-3 pt-4 border-t">
                                    <button type="button" onclick="closeEditProductModal()"
                                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg font-medium">
                                        Cancel
                                    </button>
                                    <button type="submit"
                                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                                        Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Add form submit handler
            document.getElementById('edit-product-form').addEventListener('submit', function(e) {
                e.preventDefault();
                saveProductChanges(product.id);
            });
        }

        function closeEditProductModal() {
            const modal = document.getElementById('edit-product-modal');
            if (modal) {
                modal.remove();
            }
        }

        async function saveProductChanges(productId) {
            const formData = {
                name: document.getElementById('edit-product-name').value,
                category: document.getElementById('edit-product-category').value,
                description: document.getElementById('edit-product-description').value,
                price: parseFloat(document.getElementById('edit-product-price').value),
                stock_quantity: parseInt(document.getElementById('edit-product-stock').value),
                low_stock_threshold: parseInt(document.getElementById('edit-product-low-stock').value),
                image_url: document.getElementById('edit-product-image').value,
                is_active: document.getElementById('edit-product-active').checked,
                bulk_price: document.getElementById('edit-product-bulk').checked ? formData.price * 0.9 : null
            };

            try {
                // Get authentication headers
                const headers = {
                    'Content-Type': 'application/json',
                };

                // Add authentication token if available
                const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch(`/api/admin/products/${productId}`, {
                    method: 'PUT',
                    headers: headers,
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Product updated successfully!', 'success');
                    closeEditProductModal();
                    loadAdminProducts(); // Refresh the products list
                } else {
                    showToast('Failed to update product: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Error updating product:', error);
                showToast('Error updating product: ' + error.message, 'error');
            }
        }

        function updateStock(productId) {
            const newStock = prompt('Enter new stock quantity:');
            if (newStock && !isNaN(newStock)) {
                showToast(`Stock update functionality will be implemented with proper API`, 'info');
            }
        }

        function showAddProductForm() {
            showToast('Add product functionality will be implemented with proper forms', 'info');
        }

        async function runDailyCron() {
            try {
                showToast('Running daily cron jobs...', 'info');

                const response = await fetch('/api/admin/run-daily-cron', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Daily cron jobs completed successfully!', 'success');
                    // Reload stock alerts to show updated data
                    loadStockAlerts();
                } else {
                    showToast('Failed to run daily cron jobs: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('Error running daily cron jobs: ' + error.message, 'error');
            }
        }

        // Display products
        function displayProducts(products) {
            const grid = document.getElementById('products-grid');
            grid.innerHTML = '';

            if (products.length === 0) {
                grid.innerHTML = '<div class="col-span-full text-center py-8 text-gray-500">No products found</div>';
                return;
            }

            products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow';

                // Handle image with proper fallback - use specific medical product images
                const getProductImageUrl = (product) => {
                    if (product.image_url && product.image_url.trim() !== '' && !product.image_url.includes('unsplash.com/photo-**********-0eb30cd8c063')) {
                        return product.image_url;
                    }

                    // Map specific products to their appropriate images
                    const productName = product.name.toLowerCase();

                    if (productName.includes('disposable syringe') || productName.includes('syringe')) {
                        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop';
                    } else if (productName.includes('nitrile') && productName.includes('glove')) {
                        return 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop';
                    } else if (productName.includes('surgical') && productName.includes('mask')) {
                        return 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop';
                    } else if (productName.includes('digital thermometer') || productName.includes('thermometer')) {
                        return 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=300&h=200&fit=crop';
                    } else if (productName.includes('antiseptic solution') || productName.includes('antiseptic')) {
                        return 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop';
                    } else if (productName.includes('surgical scissors') || productName.includes('scissors')) {
                        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop';
                    } else if (productName.includes('gauze bandage') || productName.includes('bandage')) {
                        return 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop';
                    } else if (productName.includes('alcohol swab') || productName.includes('swab')) {
                        return 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop';
                    } else if (productName.includes('blood pressure cuff') || productName.includes('pressure cuff')) {
                        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop';
                    } else {
                        return 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=300&h=200&fit=crop';
                    }
                };

                const imageUrl = getProductImageUrl(product);

                productCard.innerHTML = `
                    <!-- Product Image -->
                    <div class="relative h-48 bg-gray-100">
                        <img src="${imageUrl}"
                             alt="${product.name}"
                             class="w-full h-full object-cover"
                             onerror="this.src='https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'"
                             onload="console.log('Image loaded:', this.src)"
                             style="background-color: #f3f4f6;">
                        ${product.is_low_stock ? '<div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">Low Stock</div>' : ''}
                        ${product.bulk_price ? '<div class="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs">Bulk Available</div>' : ''}
                    </div>

                    <!-- Product Details -->
                    <div class="p-4">
                        <!-- Product Name & Category -->
                        <div class="mb-3">
                            <h3 class="text-lg font-semibold text-gray-800 mb-1 line-clamp-2">${product.name}</h3>
                            <span class="inline-block bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                                ${product.category || 'General'}
                            </span>
                        </div>

                        <!-- Description -->
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">${product.description || 'No description available'}</p>

                        <!-- Product Info Grid -->
                        <div class="grid grid-cols-2 gap-2 mb-3 text-xs text-gray-500">
                            <div>
                                <span class="font-medium">📦 Packaging:</span><br>
                                <span>${product.packaging || 'N/A'}</span>
                            </div>
                            <div>
                                <span class="font-medium">🏭 Manufacturer:</span><br>
                                <span>${product.manufacturer || 'N/A'}</span>
                            </div>
                            <div>
                                <span class="font-medium">📊 Stock:</span><br>
                                <span class="${product.is_low_stock ? 'text-red-600 font-medium' : 'text-green-600'}">${product.stock_quantity} units</span>
                            </div>
                            <div>
                                <span class="font-medium">📅 Expiry:</span><br>
                                <span>${product.expiry_date || 'N/A'}</span>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="mb-4">
                            <div class="flex items-baseline gap-2">
                                <span class="text-2xl font-bold text-primary-600">$${product.unit_price}</span>
                                <span class="text-sm text-gray-500">per unit</span>
                            </div>
                            ${product.bulk_price ? `
                                <div class="flex items-baseline gap-2 mt-1">
                                    <span class="text-lg font-semibold text-green-600">$${product.bulk_price}</span>
                                    <span class="text-xs text-gray-500">bulk price</span>
                                </div>
                            ` : ''}
                        </div>

                        <!-- Add to Cart Section -->
                        <div class="flex items-center gap-2">
                            <div class="flex items-center border rounded">
                                <button onclick="decreaseQuantity(${product.id})" class="px-2 py-1 hover:bg-gray-100">-</button>
                                <input type="number" id="qty-${product.id}" value="1" min="1" max="${product.stock_quantity}"
                                       class="w-12 text-center border-0 focus:ring-0">
                                <button onclick="increaseQuantity(${product.id})" class="px-2 py-1 hover:bg-gray-100">+</button>
                            </div>
                            <button onclick="addToCart(${product.id})"
                                    class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded font-medium transition-colors ${product.stock_quantity === 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                                    ${product.stock_quantity === 0 ? 'disabled' : ''}>
                                ${product.stock_quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
                            </button>
                        </div>
                    </div>
                `;
                grid.appendChild(productCard);
            });
        }

        // Quantity control functions
        function increaseQuantity(productId) {
            const input = document.getElementById(`qty-${productId}`);
            const max = parseInt(input.getAttribute('max'));
            if (parseInt(input.value) < max) {
                input.value = parseInt(input.value) + 1;
            }
        }

        function decreaseQuantity(productId) {
            const input = document.getElementById(`qty-${productId}`);
            if (parseInt(input.value) > 1) {
                input.value = parseInt(input.value) - 1;
            }
        }

        // Cart management functions
        function saveCart() {
            localStorage.setItem('gracemedical_cart', JSON.stringify(cart));
            updateCartCount();
        }

        function updateCartCount() {
            const count = cart.reduce((total, item) => total + item.quantity, 0);
            const countElement = document.getElementById('cart-count');
            if (count > 0) {
                countElement.textContent = count;
                countElement.classList.remove('hidden');
            } else {
                countElement.classList.add('hidden');
            }
        }

        function addToCart(productId) {
            const quantity = parseInt(document.getElementById(`qty-${productId}`).value);
            const product = products.find(p => p.id === productId);

            if (!product) return;

            // Check if item already in cart
            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.unit_price,
                    bulk_price: product.bulk_price,
                    image_url: product.image_url,
                    quantity: quantity,
                    stock_quantity: product.stock_quantity
                });
            }

            saveCart();

            // Show success message
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            toast.textContent = `Added ${quantity} x ${product.name} to cart!`;
            document.body.appendChild(toast);

            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cart-items');
            const cartEmpty = document.getElementById('cart-empty');
            const cartSummary = document.getElementById('cart-summary');
            const cartTotal = document.getElementById('cart-total');

            if (cart.length === 0) {
                cartItems.innerHTML = '';
                cartEmpty.classList.remove('hidden');
                cartSummary.classList.add('hidden');
                return;
            }

            cartEmpty.classList.add('hidden');
            cartSummary.classList.remove('hidden');

            // Display cart items
            cartItems.innerHTML = cart.map(item => `
                <div class="flex items-center gap-4 p-4 border rounded-lg">
                    <img src="${item.image_url || 'https://via.placeholder.com/80x80/e5e7eb/6b7280?text=Medical'}"
                         alt="${item.name}"
                         class="w-16 h-16 object-cover rounded"
                         onerror="this.src='https://via.placeholder.com/80x80/e5e7eb/6b7280?text=Medical'">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800">${item.name}</h4>
                        <p class="text-sm text-gray-600">$${item.price} each</p>
                        ${item.bulk_price ? `<p class="text-xs text-green-600">Bulk: $${item.bulk_price}</p>` : ''}
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="updateCartItemQuantity(${item.id}, ${item.quantity - 1})"
                                class="w-8 h-8 flex items-center justify-center border rounded hover:bg-gray-100"
                                ${item.quantity <= 1 ? 'disabled class="opacity-50"' : ''}>-</button>
                        <span class="w-8 text-center">${item.quantity}</span>
                        <button onclick="updateCartItemQuantity(${item.id}, ${item.quantity + 1})"
                                class="w-8 h-8 flex items-center justify-center border rounded hover:bg-gray-100"
                                ${item.quantity >= item.stock_quantity ? 'disabled class="opacity-50"' : ''}>+</button>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold">$${(item.price * item.quantity).toFixed(2)}</p>
                        <button onclick="removeFromCart(${item.id})"
                                class="text-red-500 hover:text-red-700 text-sm">Remove</button>
                    </div>
                </div>
            `).join('');

            // Calculate total
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartTotal.textContent = `$${total.toFixed(2)}`;
        }

        function updateCartItemQuantity(productId, newQuantity) {
            if (newQuantity <= 0) {
                removeFromCart(productId);
                return;
            }

            const item = cart.find(item => item.id === productId);
            if (item) {
                item.quantity = Math.min(newQuantity, item.stock_quantity);
                saveCart();
                updateCartDisplay();
            }
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            saveCart();
            updateCartDisplay();
        }

        function clearCart() {
            if (confirm('Are you sure you want to clear your cart?')) {
                cart = [];
                saveCart();
                updateCartDisplay();
            }
        }

        function proceedToCheckout() {
            if (cart.length === 0) {
                alert('Your cart is empty!');
                return;
            }

            if (!currentUser) {
                alert('Please login to proceed to checkout');
                hideCart();
                showLogin();
                return;
            }

            hideCart();
            showCheckout();
        }

        function updateCheckoutDisplay() {
            const checkoutItems = document.getElementById('checkout-items');
            const checkoutTotal = document.getElementById('checkout-total');

            // Display checkout items
            checkoutItems.innerHTML = cart.map(item => `
                <div class="flex items-center gap-3 p-3 border rounded">
                    <img src="${item.image_url || 'https://via.placeholder.com/50x50/e5e7eb/6b7280?text=Medical'}"
                         alt="${item.name}"
                         class="w-12 h-12 object-cover rounded"
                         onerror="this.src='https://via.placeholder.com/50x50/e5e7eb/6b7280?text=Medical'">
                    <div class="flex-1">
                        <h5 class="font-medium text-sm">${item.name}</h5>
                        <p class="text-xs text-gray-600">$${item.price} x ${item.quantity}</p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-sm">$${(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                </div>
            `).join('');

            // Calculate total
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            checkoutTotal.textContent = `$${total.toFixed(2)}`;

            // Pre-fill user information if logged in
            if (currentUser) {
                document.getElementById('checkout-first-name').value = currentUser.first_name || '';
                document.getElementById('checkout-last-name').value = currentUser.last_name || '';
                document.getElementById('checkout-email').value = currentUser.email || '';
            }
        }

        async function handleCheckout(event) {
            event.preventDefault();

            const formData = {
                first_name: document.getElementById('checkout-first-name').value,
                last_name: document.getElementById('checkout-last-name').value,
                email: document.getElementById('checkout-email').value,
                phone: document.getElementById('checkout-phone').value,
                shipping_address: document.getElementById('checkout-address').value,
                notes: document.getElementById('checkout-notes').value,
                items: cart.map(item => ({
                    product_id: item.id,
                    quantity: item.quantity,
                    unit_price: item.price
                })),
                total_amount: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
            };

            try {
                const response = await fetch('/api/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (response.ok) {
                    // Clear cart
                    cart = [];
                    saveCart();

                    // Hide checkout modal
                    hideCheckout();

                    // Show success message
                    alert(`Order placed successfully! Order number: ${data.order_number}`);

                    // Optionally redirect to order confirmation or orders page
                    console.log('Order placed:', data);
                } else {
                    alert(data.error || 'Failed to place order');
                }
            } catch (error) {
                alert('Error placing order: ' + error.message);
            }
        }



        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Grace Medical Limited - Web Application Loaded');

            // Load user from localStorage
            const savedUser = localStorage.getItem('gracemedical_user');
            if (savedUser) {
                currentUser = JSON.parse(savedUser);
            }

            updateCartCount(); // Initialize cart count display
            updateNavigation(); // Initialize navigation based on login status
        });
    </script>
</body>
</html>
    '''

# API Root Route
@app.route('/api')
def api_root():
    return jsonify({
        'message': 'Grace Medical API v1.0.0',
        'status': 'success',
        'description': 'Medical supply management system API',
        'documentation': {
            'authentication': 'Use POST /api/auth/login to get JWT token',
            'products': 'GET /api/products for product catalog',
            'orders': 'GET/POST /api/orders for order management (requires auth)',
            'admin': 'Admin routes require X-API-Key header'
        },
        'endpoints': {
            'auth': '/api/auth/login',
            'products': '/api/products',
            'orders': '/api/orders',
            'categories': '/api/categories',
            'health': '/api/health'
        }
    })

# Health Check Route
@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'jwt_available': JWT_AVAILABLE,
        'cors_available': CORS_AVAILABLE,
        'mock_data': {
            'products': len(MOCK_PRODUCTS),
            'users': len(MOCK_USERS),
            'orders': len(MOCK_ORDERS)
        }
    })



# Authentication Routes
@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check if user already exists
        cursor.execute('SELECT id FROM users WHERE email = ?', (data['email'],))
        if cursor.fetchone():
            return jsonify({'error': 'Email already registered'}), 400

        # Create new user
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, phone, is_verified)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            data['email'],
            generate_password_hash(data['password']),
            data['first_name'],
            data['last_name'],
            data.get('phone', ''),
            True  # Auto-verify for now, can implement email verification later
        ))

        conn.commit()
        conn.close()

        return jsonify({
            'message': 'Registration successful. You can now log in.',
            'user': {
                'email': data['email'],
                'first_name': data['first_name'],
                'last_name': data['last_name']
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()

        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, email, password_hash, first_name, last_name, phone, is_admin, is_verified
            FROM users WHERE email = ?
        ''', (data['email'],))

        user = cursor.fetchone()
        conn.close()

        if not user or not check_password_hash(user[2], data['password']):
            return jsonify({'error': 'Invalid email or password'}), 401

        if not user[7]:  # is_verified
            return jsonify({'error': 'Please verify your email before logging in'}), 403

        # Create access token
        access_token = f"user_{user[0]}_{uuid.uuid4().hex}"

        return jsonify({
            'access_token': access_token,
            'user': {
                'id': user[0],
                'email': user[1],
                'first_name': user[3],
                'last_name': user[4],
                'phone': user[5],
                'is_admin': bool(user[6])
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/verify-email', methods=['POST'])
def verify_email():
    try:
        data = request.get_json()
        token = data.get('token')

        if not token:
            return jsonify({'error': 'Verification token is required'}), 400

        # Mock verification - just return success for testing
        return jsonify({'message': 'Email verified successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Product Routes
@app.route('/api/products', methods=['GET'])
def get_products():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        search = request.args.get('search')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Build query
        query = 'SELECT * FROM products WHERE is_active = 1'
        params = []

        if category:
            query += ' AND category = ?'
            params.append(category)

        if search:
            query += ' AND (name LIKE ? OR description LIKE ?)'
            params.extend([f'%{search}%', f'%{search}%'])

        # Get total count
        count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # Add pagination
        query += ' LIMIT ? OFFSET ?'
        params.extend([per_page, (page - 1) * per_page])

        cursor.execute(query, params)
        products = cursor.fetchall()
        conn.close()

        # Convert to dict format
        product_list = []
        for product in products:
            product_dict = {
                'id': product[0],
                'name': product[1],
                'description': product[2],
                'packaging': product[3],
                'unit_price': product[4],
                'bulk_price': product[5],
                'manufacturer': product[6],
                'expiry_date': product[7],
                'stock_quantity': product[8],
                'reorder_level': product[9],
                'category': product[10],
                'image_url': product[11] or f"https://via.placeholder.com/300x200/e5e7eb/6b7280?text={product[1].replace(' ', '+').replace('&', 'and')}",
                'is_active': bool(product[12]),
                'is_low_stock': product[8] <= product[9],
                'is_near_expiry': False  # Will implement proper logic later
            }
            product_list.append(product_dict)

        return jsonify({
            'products': product_list,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM products WHERE id = ? AND is_active = 1', (product_id,))
        product = cursor.fetchone()
        conn.close()

        if not product:
            return jsonify({'error': 'Product not found'}), 404

        product_dict = {
            'id': product[0],
            'name': product[1],
            'description': product[2],
            'packaging': product[3],
            'unit_price': product[4],
            'bulk_price': product[5],
            'manufacturer': product[6],
            'expiry_date': product[7],
            'stock_quantity': product[8],
            'reorder_level': product[9],
            'category': product[10],
            'image_url': product[11] or f"https://via.placeholder.com/300x200/e5e7eb/6b7280?text={product[1].replace(' ', '+').replace('&', 'and')}",
            'is_active': bool(product[12]),
            'is_low_stock': product[8] <= product[9],
            'is_near_expiry': False
        }

        return jsonify(product_dict), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/products', methods=['POST'])
@require_api_key
def create_product():
    try:
        data = request.get_json()

        required_fields = ['name', 'unit_price']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        # Create new product (mock implementation)
        new_product = {
            'id': len(MOCK_PRODUCTS) + 1,
            'name': data['name'],
            'description': data.get('description', ''),
            'packaging': data.get('packaging', ''),
            'unit_price': float(data['unit_price']),
            'bulk_price': float(data['bulk_price']) if data.get('bulk_price') else None,
            'manufacturer': data.get('manufacturer', ''),
            'expiry_date': data.get('expiry_date', '2025-12-31'),
            'stock_quantity': int(data.get('stock_quantity', 0)),
            'reorder_level': int(data.get('reorder_level', 10)),
            'category': data.get('category', 'General'),
            'image_url': data.get('image_url', '/images/default-product.jpg'),
            'is_active': True,
            'is_low_stock': False,
            'is_near_expiry': False
        }

        MOCK_PRODUCTS.append(new_product)

        return jsonify(new_product), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/products/<int:product_id>', methods=['PUT'])
@require_api_key
def update_product(product_id):
    try:
        # Find product in mock data
        product = next((p for p in MOCK_PRODUCTS if p['id'] == product_id), None)
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        data = request.get_json()

        # Update fields if provided
        if 'name' in data:
            product['name'] = data['name']
        if 'description' in data:
            product['description'] = data['description']
        if 'packaging' in data:
            product['packaging'] = data['packaging']
        if 'unit_price' in data:
            product['unit_price'] = float(data['unit_price'])
        if 'bulk_price' in data:
            product['bulk_price'] = float(data['bulk_price']) if data['bulk_price'] else None
        if 'manufacturer' in data:
            product['manufacturer'] = data['manufacturer']
        if 'expiry_date' in data:
            product['expiry_date'] = data['expiry_date'] if data['expiry_date'] else '2025-12-31'
        if 'stock_quantity' in data:
            product['stock_quantity'] = int(data['stock_quantity'])
        if 'reorder_level' in data:
            product['reorder_level'] = int(data['reorder_level'])
        if 'category' in data:
            product['category'] = data['category']
        if 'image_url' in data:
            product['image_url'] = data['image_url']
        if 'is_active' in data:
            product['is_active'] = bool(data['is_active'])

        return jsonify(product), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500





@app.route('/api/admin/low-stock', methods=['GET'])
@require_api_key
def get_low_stock_products():
    try:
        products = [p for p in MOCK_PRODUCTS if p['stock_quantity'] <= p['reorder_level'] and p['is_active']]
        return jsonify(products), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/near-expiry', methods=['GET'])
@require_api_key
def get_near_expiry_products():
    try:
        days = request.args.get('days', 30, type=int)
        near_expiry_date = (datetime.now() + timedelta(days=days)).date()

        products = []
        for p in MOCK_PRODUCTS:
            if p['is_active']:
                expiry_date = datetime.strptime(p['expiry_date'], '%Y-%m-%d').date()
                if expiry_date <= near_expiry_date and expiry_date >= datetime.now().date():
                    products.append(p)

        return jsonify(products), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/reports', methods=['GET'])
@require_api_key
def get_reports():
    try:
        # Get date range
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        else:
            start_date = datetime.now().date() - timedelta(days=30)

        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        else:
            end_date = datetime.now().date()

        # Filter orders by date range (mock implementation)
        orders = []
        for order in MOCK_ORDERS:
            order_date = datetime.fromisoformat(order['created_at']).date()
            if start_date <= order_date <= end_date:
                orders.append(order)

        # Calculate statistics
        total_orders = len(orders)
        total_revenue = sum(order['total_amount'] for order in orders)

        status_counts = {}
        for order in orders:
            status_counts[order['status']] = status_counts.get(order['status'], 0) + 1

        # Top products (mock data)
        top_products = [
            {'product_name': 'Disposable Syringes 10ml', 'quantity_sold': 150, 'revenue': 3898.50},
            {'product_name': 'Nitrile Examination Gloves', 'quantity_sold': 120, 'revenue': 2220.00},
            {'product_name': 'Surgical Face Masks', 'quantity_sold': 100, 'revenue': 1299.00}
        ]

        # Count low stock and near expiry products
        low_stock_count = len([p for p in MOCK_PRODUCTS if p['stock_quantity'] <= p['reorder_level']])

        near_expiry_count = 0
        thirty_days_from_now = (datetime.now() + timedelta(days=30)).date()
        for p in MOCK_PRODUCTS:
            expiry_date = datetime.strptime(p['expiry_date'], '%Y-%m-%d').date()
            if expiry_date <= thirty_days_from_now and expiry_date >= datetime.now().date():
                near_expiry_count += 1

        return jsonify({
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'status_counts': status_counts,
            'top_products': top_products,
            'low_stock_count': low_stock_count,
            'near_expiry_count': near_expiry_count
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Notification Routes (Mock implementation)
@app.route('/api/notifications', methods=['GET'])
@require_auth
def get_notifications(current_user):
    try:
        # Mock notifications - return empty list for now
        return jsonify({
            'notifications': [],
            'total': 0,
            'pages': 0,
            'current_page': 1,
            'unread_count': 0
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/<int:notification_id>/read', methods=['PUT'])
@require_auth
def mark_notification_read(current_user, notification_id):
    try:
        # Mock implementation - just return success
        return jsonify({'message': 'Notification marked as read'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Order Routes
@app.route('/api/orders', methods=['POST'])
def create_order():
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['first_name', 'last_name', 'email', 'phone', 'shipping_address', 'items', 'total_amount']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        if not data['items'] or len(data['items']) == 0:
            return jsonify({'error': 'Order must contain at least one item'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Generate order number
        order_number = f"GM{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6].upper()}"

        # Create order
        cursor.execute('''
            INSERT INTO orders (
                user_id, order_number, status, total_amount,
                shipping_address, phone, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            1,  # Default user ID for now (will implement proper user system later)
            order_number,
            'Pending',
            float(data['total_amount']),
            data['shipping_address'],
            data['phone'],
            data.get('notes', '')
        ))

        order_id = cursor.lastrowid

        # Add order items
        for item in data['items']:
            cursor.execute('''
                INSERT INTO order_items (
                    order_id, product_id, quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?)
            ''', (
                order_id,
                item['product_id'],
                item['quantity'],
                item['unit_price'],
                item['quantity'] * item['unit_price']
            ))

            # Update product stock
            cursor.execute('''
                UPDATE products
                SET stock_quantity = stock_quantity - ?
                WHERE id = ?
            ''', (item['quantity'], item['product_id']))

        conn.commit()

        # Get order details for invoice and notification
        cursor.execute('''
            SELECT o.*, oi.product_id, oi.quantity, oi.unit_price, oi.total_price, p.name
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE o.id = ?
        ''', (order_id,))

        order_details = cursor.fetchall()
        conn.close()

        # Prepare order data for invoice and notification
        order_data = {
            'order_id': order_id,
            'order_number': order_number,
            'status': 'Pending',
            'total_amount': data['total_amount'],
            'shipping_address': data['shipping_address'],
            'phone': data['phone'],
            'notes': data.get('notes', ''),
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'items': []
        }

        # Add items to order data
        for detail in order_details:
            if detail[11]:  # product name exists
                order_data['items'].append({
                    'product_name': detail[11],
                    'quantity': detail[8],
                    'unit_price': detail[9],
                    'total_price': detail[10]
                })

        # Generate PDF invoice
        invoice_html = generate_invoice_pdf(order_data)

        # Send email notification to admin
        send_order_notification_email(order_data)

        return jsonify({
            'message': 'Order placed successfully',
            'order_id': order_id,
            'order_number': order_number,
            'status': 'Pending',
            'total_amount': data['total_amount'],
            'invoice_generated': invoice_html is not None
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/orders', methods=['GET'])
def get_orders():
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT o.id, o.user_id, o.order_number, o.status, o.total_amount,
                   o.shipping_address, o.phone, o.notes,
                   o.created_at, COUNT(oi.id) as item_count
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            GROUP BY o.id, o.user_id, o.order_number, o.status, o.total_amount,
                     o.shipping_address, o.phone, o.notes, o.created_at
            ORDER BY o.created_at DESC
        ''')

        orders = []
        for row in cursor.fetchall():
            orders.append({
                'id': row[0],
                'user_id': row[1],
                'order_number': row[2],
                'status': row[3],
                'total_amount': row[4],
                'shipping_address': row[5] or '',
                'billing_address': '',  # Not in current schema
                'phone': row[6] or '',
                'notes': row[7] or '',
                'created_at': row[8],
                'updated_at': row[8],  # Use created_at as fallback
                'item_count': row[9]
            })

        conn.close()
        return jsonify({'orders': orders}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get order details
        cursor.execute('SELECT * FROM orders WHERE id = ?', (order_id,))
        order = cursor.fetchone()

        if not order:
            return jsonify({'error': 'Order not found'}), 404

        # Get order items with product details
        cursor.execute('''
            SELECT oi.*, p.name, p.image_url
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ''', (order_id,))

        items = []
        for item in cursor.fetchall():
            items.append({
                'id': item[0],
                'product_id': item[2],
                'quantity': item[3],
                'unit_price': item[4],
                'total_price': item[5],
                'product_name': item[6],
                'product_image': item[7]
            })

        conn.close()

        order_dict = {
            'id': order[0],
            'user_id': order[1],
            'order_number': order[2],
            'status': order[3],
            'total_amount': order[4],
            'shipping_address': order[5],
            'billing_address': order[6],
            'phone': order[7],
            'notes': order[8],
            'created_at': order[9],
            'updated_at': order[10],
            'items': items
        }

        return jsonify(order_dict), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/orders/<int:order_id>/invoice', methods=['GET'])
def get_order_invoice(order_id):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get order details
        cursor.execute('SELECT * FROM orders WHERE id = ?', (order_id,))
        order = cursor.fetchone()

        if not order:
            return jsonify({'error': 'Order not found'}), 404

        # Get order items with product details
        cursor.execute('''
            SELECT oi.*, p.name
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ''', (order_id,))

        items = []
        for item in cursor.fetchall():
            items.append({
                'product_name': item[6],
                'quantity': item[3],
                'unit_price': item[4],
                'total_price': item[5]
            })

        conn.close()

        order_data = {
            'order_number': order[2],
            'status': order[3],
            'total_amount': order[4],
            'shipping_address': order[5],
            'phone': order[7],
            'notes': order[8],
            'created_at': order[9],
            'items': items
        }

        invoice_html = generate_invoice_pdf(order_data)

        if invoice_html:
            return invoice_html, 200, {'Content-Type': 'text/html'}
        else:
            return jsonify({'error': 'Failed to generate invoice'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/stock-alerts', methods=['GET'])
def get_stock_alerts():
    try:
        alerts = check_low_stock_and_expiry()
        return jsonify(alerts), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/run-daily-cron', methods=['POST'])
def run_daily_cron():
    """Manual trigger for daily cron jobs (for testing)"""
    try:
        run_daily_cron_jobs()
        return jsonify({'message': 'Daily cron jobs executed successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Utility Routes
@app.route('/api/categories', methods=['GET'])
def get_categories():
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND is_active = 1')
        categories = [row[0] for row in cursor.fetchall()]
        conn.close()

        return jsonify(categories), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/products/<int:product_id>', methods=['PUT'])
@require_auth
def admin_update_product(current_user, product_id):
    """Update a product (admin only)"""
    if not current_user.get('is_admin'):
        return jsonify({'error': 'Admin access required'}), 403

    try:
        data = request.get_json()

        # Validate required fields
        if not data.get('name'):
            return jsonify({'error': 'Product name is required'}), 400

        if not data.get('price') or float(data.get('price')) < 0:
            return jsonify({'error': 'Valid price is required'}), 400

        if data.get('stock_quantity') is None or int(data.get('stock_quantity')) < 0:
            return jsonify({'error': 'Valid stock quantity is required'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check if product exists
        cursor.execute('SELECT id FROM products WHERE id = ?', (product_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({'error': 'Product not found'}), 404

        # Update product
        cursor.execute('''
            UPDATE products
            SET name = ?, category = ?, description = ?, price = ?,
                stock_quantity = ?, low_stock_threshold = ?, image_url = ?,
                is_active = ?, bulk_price = ?
            WHERE id = ?
        ''', (
            data.get('name'),
            data.get('category'),
            data.get('description'),
            float(data.get('price')),
            int(data.get('stock_quantity')),
            int(data.get('low_stock_threshold', 10)),
            data.get('image_url'),
            bool(data.get('is_active', True)),
            float(data.get('bulk_price')) if data.get('bulk_price') else None,
            product_id
        ))

        conn.commit()
        conn.close()

        return jsonify({'message': 'Product updated successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
@require_auth
def upload_file(current_user):
    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    
    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Get the file URL
        file_url = f"/uploads/{filename}"
        
        return jsonify({
            'message': 'File uploaded successfully',
            'filename': filename,
            'url': file_url
        }), 201
    
    return jsonify({'error': 'File type not allowed'}), 400

# Route to serve uploaded files
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# Import order status manager
try:
    from order_status_manager import order_status_manager, ORDER_STATUS_FLOW
    ORDER_STATUS_MANAGER_AVAILABLE = True
except ImportError:
    ORDER_STATUS_MANAGER_AVAILABLE = False
    print("⚠️ Order status manager not available")

# Order Status Management Routes
@app.route('/api/orders/<int:order_id>/status', methods=['PUT'])
@require_auth
def update_order_status(current_user, order_id):
    """Update order status (admin only)"""
    if not current_user.get('is_admin'):
        return jsonify({'error': 'Admin access required'}), 403

    if not ORDER_STATUS_MANAGER_AVAILABLE:
        return jsonify({'error': 'Order status manager not available'}), 503

    try:
        data = request.get_json()
        new_status = data.get('status')
        change_reason = data.get('reason', '')
        tracking_number = data.get('tracking_number')

        if not new_status:
            return jsonify({'error': 'Status is required'}), 400

        if new_status not in ORDER_STATUS_FLOW:
            return jsonify({'error': 'Invalid status'}), 400

        success = order_status_manager.update_order_status(
            order_id=order_id,
            new_status=new_status,
            changed_by=f"admin_{current_user.get('email', 'unknown')}",
            change_reason=change_reason,
            tracking_number=tracking_number
        )

        if success:
            return jsonify({'message': 'Order status updated successfully'}), 200
        else:
            return jsonify({'error': 'Failed to update order status'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/orders/<int:order_id>/status-history', methods=['GET'])
@require_auth
def get_order_status_history(current_user, order_id):
    """Get order status change history"""
    if not ORDER_STATUS_MANAGER_AVAILABLE:
        return jsonify({'error': 'Order status manager not available'}), 503

    try:
        history = order_status_manager.get_order_status_history(order_id)
        return jsonify({'history': history}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/order-statuses', methods=['GET'])
@require_auth
def get_order_statuses(current_user):
    """Get available order statuses and their configurations"""
    if not current_user.get('is_admin'):
        return jsonify({'error': 'Admin access required'}), 403

    return jsonify({'statuses': ORDER_STATUS_FLOW}), 200

@app.route('/api/admin/run-auto-status-updates', methods=['POST'])
@require_auth
def run_auto_status_updates(current_user):
    """Manually trigger automatic status updates"""
    if not current_user.get('is_admin'):
        return jsonify({'error': 'Admin access required'}), 403

    if not ORDER_STATUS_MANAGER_AVAILABLE:
        return jsonify({'error': 'Order status manager not available'}), 503

    try:
        order_status_manager.run_auto_status_updates()
        return jsonify({'message': 'Auto status updates completed'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Manual migration endpoint for debugging
@app.route('/api/admin/migrate-database', methods=['POST'])
@require_auth
def manual_migrate_database(current_user):
    """Manually trigger database migration"""
    if not current_user.get('is_admin'):
        return jsonify({'error': 'Admin access required'}), 403

    try:
        migrate_database()
        return jsonify({'message': 'Database migration completed successfully'}), 200
    except Exception as e:
        return jsonify({'error': f'Migration failed: {str(e)}'}), 500

if __name__ == '__main__':
    # Initialize database and import data
    print("Initializing Grace Medical Limited Backend...")
    init_database()
    migrate_database()  # Apply any pending migrations
    import_products_from_csv()
    create_admin_user()

    # Initialize order status manager
    if ORDER_STATUS_MANAGER_AVAILABLE:
        print("\n📋 Initializing order status management...")
        try:
            # Initialize the order status manager database tables
            order_status_manager.init_status_tracking_table()
            print("✅ Order status manager initialized successfully")

            # Optionally start the background scheduler
            try:
                from order_scheduler import start_order_scheduler
                if start_order_scheduler():
                    print("✅ Order status scheduler started")
                else:
                    print("⚠️ Order status scheduler failed to start")
            except ImportError:
                print("⚠️ Order status scheduler not available")

        except Exception as e:
            print(f"❌ Failed to initialize order status manager: {e}")
    else:
        print("⚠️ Order status manager not available")

    # Run daily cron jobs (stock and expiry check)
    print("\n🕐 Running daily cron jobs...")
    run_daily_cron_jobs()

    print("\nGrace Medical Limited Backend Server")
    print("====================================")
    print("✅ Features Implemented:")
    print("📦 Product catalog with CSV import (100 items)")
    print("🛒 Shopping cart with quantity management")
    print("💳 Complete checkout system")
    print("👤 User registration and authentication")
    print("📋 Order management and tracking")
    print("👨‍💼 Admin dashboard with product/order management")
    print("📄 PDF invoice generation")
    print("📧 Email notifications for new orders")
    print("⚠️ Daily stock and expiry alerts")
    print("🔔 In-app notification system")
    print("🔄 Automatic order status transitions")
    print("📧 Email notifications for status changes")
    print("👨‍💼 Manual admin order status management")
    print("====================================")
    print("🌐 Server starting at: http://localhost:5000")
    print("🔑 Admin login: <EMAIL> / admin123")
    print("🔐 API Key:", API_KEY)
    print("🛍️ E-commerce Features: Enabled")
    print("====================================")

    app.run(debug=True, host='0.0.0.0', port=5000)

# ============================================================================
# E-COMMERCE ROUTES
# ============================================================================

# Shopping Cart Routes
@app.route('/api/cart', methods=['GET'])
def get_cart():
    """Get shopping cart contents"""
    try:
        user_id = session.get('user_id')
        session_id = session.get('session_id', 'anonymous')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        if user_id:
            cursor.execute('''
                SELECT sc.id, sc.product_id, sc.quantity, sc.price, p.name, p.image_url, p.stock_quantity
                FROM shopping_cart sc
                JOIN products p ON sc.product_id = p.id
                WHERE sc.user_id = ?
            ''', (user_id,))
        else:
            cursor.execute('''
                SELECT sc.id, sc.product_id, sc.quantity, sc.price, p.name, p.image_url, p.stock_quantity
                FROM shopping_cart sc
                JOIN products p ON sc.product_id = p.id
                WHERE sc.session_id = ?
            ''', (session_id,))

        cart_items = []
        total = 0

        for row in cursor.fetchall():
            item = {
                'id': row[0],
                'product_id': row[1],
                'quantity': row[2],
                'price': row[3],
                'name': row[4],
                'image_url': row[5],
                'stock_quantity': row[6],
                'subtotal': row[2] * row[3]
            }
            cart_items.append(item)
            total += item['subtotal']

        conn.close()

        return jsonify({
            'items': cart_items,
            'total': total,
            'item_count': len(cart_items)
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cart/add', methods=['POST'])
def add_to_cart():
    """Add item to shopping cart"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        quantity = data.get('quantity', 1)

        if not product_id:
            return jsonify({'error': 'Product ID is required'}), 400

        user_id = session.get('user_id')
        session_id = session.get('session_id', 'anonymous')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get product details
        cursor.execute('SELECT price, stock_quantity FROM products WHERE id = ?', (product_id,))
        product = cursor.fetchone()

        if not product:
            return jsonify({'error': 'Product not found'}), 404

        price, stock_quantity = product

        if quantity > stock_quantity:
            return jsonify({'error': 'Insufficient stock'}), 400

        # Check if item already in cart
        if user_id:
            cursor.execute('SELECT id, quantity FROM shopping_cart WHERE user_id = ? AND product_id = ?',
                         (user_id, product_id))
        else:
            cursor.execute('SELECT id, quantity FROM shopping_cart WHERE session_id = ? AND product_id = ?',
                         (session_id, product_id))

        existing_item = cursor.fetchone()

        if existing_item:
            # Update quantity
            new_quantity = existing_item[1] + quantity
            cursor.execute('UPDATE shopping_cart SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                         (new_quantity, existing_item[0]))
        else:
            # Add new item
            cursor.execute('''
                INSERT INTO shopping_cart (user_id, session_id, product_id, quantity, price)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, session_id, product_id, quantity, price))

        conn.commit()
        conn.close()

        return jsonify({'message': 'Item added to cart successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cart/update/<int:item_id>', methods=['PUT'])
def update_cart_item(item_id):
    """Update cart item quantity"""
    try:
        data = request.get_json()
        quantity = data.get('quantity')

        if quantity is None or quantity < 0:
            return jsonify({'error': 'Valid quantity is required'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        if quantity == 0:
            # Remove item
            cursor.execute('DELETE FROM shopping_cart WHERE id = ?', (item_id,))
        else:
            # Update quantity
            cursor.execute('UPDATE shopping_cart SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                         (quantity, item_id))

        conn.commit()
        conn.close()

        return jsonify({'message': 'Cart updated successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cart/remove/<int:item_id>', methods=['DELETE'])
def remove_from_cart(item_id):
    """Remove item from cart"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM shopping_cart WHERE id = ?', (item_id,))

        conn.commit()
        conn.close()

        return jsonify({'message': 'Item removed from cart'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Enhanced Checkout Route
@app.route('/api/checkout', methods=['POST'])
def checkout():
    """Process checkout with enhanced e-commerce features"""
    try:
        data = request.get_json()

        # Get cart items
        user_id = session.get('user_id')
        session_id = session.get('session_id', 'anonymous')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get cart items
        if user_id:
            cursor.execute('''
                SELECT sc.product_id, sc.quantity, sc.price, p.name
                FROM shopping_cart sc
                JOIN products p ON sc.product_id = p.id
                WHERE sc.user_id = ?
            ''', (user_id,))
        else:
            cursor.execute('''
                SELECT sc.product_id, sc.quantity, sc.price, p.name
                FROM shopping_cart sc
                JOIN products p ON sc.product_id = p.id
                WHERE sc.session_id = ?
            ''', (session_id,))

        cart_items = cursor.fetchall()

        if not cart_items:
            return jsonify({'error': 'Cart is empty'}), 400

        # Calculate totals
        subtotal = sum(item[1] * item[2] for item in cart_items)
        tax_rate = 0.0825  # 8.25% tax
        tax_amount = subtotal * tax_rate
        shipping_amount = 9.99 if subtotal < 75 else 0  # Free shipping over $75
        total_amount = subtotal + tax_amount + shipping_amount

        # Generate order number
        order_number = f"GM{int(time.time())}"

        # Create order
        cursor.execute('''
            INSERT INTO orders (
                order_number, customer_email, customer_phone, shipping_address,
                subtotal, tax_amount, shipping_amount, total_amount,
                payment_status, fulfillment_status, status, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            order_number,
            data.get('email'),
            data.get('phone'),
            data.get('shipping_address'),
            subtotal,
            tax_amount,
            shipping_amount,
            total_amount,
            'pending',
            'unfulfilled',
            'Pending',
            data.get('notes', '')
        ))

        order_id = cursor.lastrowid

        # Add order items
        for product_id, quantity, price, name in cart_items:
            cursor.execute('''
                INSERT INTO order_items (order_id, product_id, product_name, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (order_id, product_id, name, quantity, price, quantity * price))

            # Update product stock
            cursor.execute('UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?',
                         (quantity, product_id))

        # Clear cart
        if user_id:
            cursor.execute('DELETE FROM shopping_cart WHERE user_id = ?', (user_id,))
        else:
            cursor.execute('DELETE FROM shopping_cart WHERE session_id = ?', (session_id,))

        conn.commit()
        conn.close()

        return jsonify({
            'message': 'Order placed successfully',
            'order_id': order_id,
            'order_number': order_number,
            'total_amount': total_amount
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500







