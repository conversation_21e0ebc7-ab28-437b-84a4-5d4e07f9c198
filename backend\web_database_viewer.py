#!/usr/bin/env python3
"""
Web-based Database Viewer for Grace Medical Limited
View your database tables in a web browser
"""

from flask import Flask, render_template_string, jsonify, request
import sqlite3
import os
import json
from datetime import datetime

app = Flask(__name__)

# HTML template for the database viewer
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grace Medical Limited - Database Viewer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .header { background: #0284c7; color: white; padding: 1rem; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 2rem; }
        .card-header { background: #f8fafc; padding: 1rem; border-bottom: 1px solid #e2e8f0; border-radius: 8px 8px 0 0; }
        .card-body { padding: 1rem; }
        .table-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .table-card { border: 1px solid #e2e8f0; border-radius: 6px; padding: 1rem; cursor: pointer; transition: all 0.2s; }
        .table-card:hover { border-color: #0284c7; box-shadow: 0 2px 8px rgba(2,132,199,0.1); }
        .table-card.active { border-color: #0284c7; background: #f0f9ff; }
        .table-name { font-weight: 600; color: #1e293b; margin-bottom: 0.5rem; }
        .table-info { font-size: 0.875rem; color: #64748b; }
        .data-table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
        .data-table th, .data-table td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #e2e8f0; }
        .data-table th { background: #f8fafc; font-weight: 600; color: #374151; }
        .data-table tr:hover { background: #f8fafc; }
        .schema-info { background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; padding: 1rem; margin-bottom: 1rem; }
        .column { display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; }
        .column-name { font-weight: 600; color: #0284c7; }
        .column-type { background: #e2e8f0; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; }
        .primary-key { background: #fef3c7; color: #92400e; }
        .loading { text-align: center; padding: 2rem; color: #64748b; }
        .error { background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 1rem; border-radius: 6px; margin: 1rem 0; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .stat-card { background: white; border-radius: 6px; padding: 1rem; text-align: center; border: 1px solid #e2e8f0; }
        .stat-number { font-size: 2rem; font-weight: 700; color: #0284c7; }
        .stat-label { color: #64748b; font-size: 0.875rem; }
        .query-section { margin-top: 2rem; }
        .query-input { width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; font-family: monospace; }
        .btn { background: #0284c7; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 500; }
        .btn:hover { background: #0369a1; }
        .btn-secondary { background: #6b7280; }
        .btn-secondary:hover { background: #4b5563; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗄️ Grace Medical Limited - Database Viewer</h1>
        <p>View and explore your database tables</p>
    </div>

    <div class="container">
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="tableCount">-</div>
                <div class="stat-label">Tables</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRows">-</div>
                <div class="stat-label">Total Rows</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="dbSize">-</div>
                <div class="stat-label">Database Size</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>📋 Database Tables</h2>
            </div>
            <div class="card-body">
                <div class="table-list" id="tableList">
                    <div class="loading">Loading tables...</div>
                </div>
            </div>
        </div>

        <div class="card" id="tableDetails" style="display: none;">
            <div class="card-header">
                <h2 id="tableTitle">Table Details</h2>
            </div>
            <div class="card-body">
                <div id="tableSchema"></div>
                <div id="tableData"></div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>🔍 Custom SQL Query</h2>
            </div>
            <div class="card-body">
                <textarea id="sqlQuery" class="query-input" rows="4" placeholder="Enter your SQL query here...
Example: SELECT * FROM products LIMIT 10"></textarea>
                <br><br>
                <button class="btn" onclick="executeQuery()">Execute Query</button>
                <button class="btn btn-secondary" onclick="clearQuery()">Clear</button>
                <div id="queryResult"></div>
            </div>
        </div>
    </div>

    <script>
        let currentTable = null;

        async function loadTables() {
            try {
                const response = await fetch('/api/tables');
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('tableList').innerHTML = `<div class="error">${data.error}</div>`;
                    return;
                }

                // Update stats
                document.getElementById('tableCount').textContent = data.tables.length;
                document.getElementById('totalRows').textContent = data.total_rows.toLocaleString();
                document.getElementById('dbSize').textContent = data.db_size;

                // Render tables
                const tableList = document.getElementById('tableList');
                tableList.innerHTML = data.tables.map(table => `
                    <div class="table-card" onclick="selectTable('${table.name}')">
                        <div class="table-name">${table.name}</div>
                        <div class="table-info">
                            ${table.row_count.toLocaleString()} rows • ${table.column_count} columns
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                document.getElementById('tableList').innerHTML = `<div class="error">Error loading tables: ${error.message}</div>`;
            }
        }

        async function selectTable(tableName) {
            // Update active state
            document.querySelectorAll('.table-card').forEach(card => card.classList.remove('active'));
            event.target.closest('.table-card').classList.add('active');

            currentTable = tableName;
            document.getElementById('tableTitle').textContent = `📋 ${tableName}`;
            document.getElementById('tableDetails').style.display = 'block';

            // Load table schema and data
            try {
                const response = await fetch(`/api/table/${tableName}`);
                const data = await response.json();

                if (data.error) {
                    document.getElementById('tableSchema').innerHTML = `<div class="error">${data.error}</div>`;
                    return;
                }

                // Render schema
                const schemaHtml = `
                    <div class="schema-info">
                        <h3>📊 Table Schema</h3>
                        ${data.schema.map(col => `
                            <div class="column">
                                <span class="column-name">${col.name}</span>
                                <span class="column-type ${col.pk ? 'primary-key' : ''}">${col.type}</span>
                                ${col.pk ? '<span class="column-type primary-key">PRIMARY KEY</span>' : ''}
                                ${col.notnull ? '<span class="column-type">NOT NULL</span>' : ''}
                            </div>
                        `).join('')}
                    </div>
                `;

                // Render data
                const dataHtml = data.data.length > 0 ? `
                    <h3>📄 Sample Data (first 20 rows)</h3>
                    <table class="data-table">
                        <thead>
                            <tr>${data.schema.map(col => `<th>${col.name}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${data.data.map(row => `
                                <tr>${row.map(cell => `<td>${cell === null ? '<em>NULL</em>' : cell}</td>`).join('')}</tr>
                            `).join('')}
                        </tbody>
                    </table>
                ` : '<p>No data in this table.</p>';

                document.getElementById('tableSchema').innerHTML = schemaHtml;
                document.getElementById('tableData').innerHTML = dataHtml;

            } catch (error) {
                document.getElementById('tableSchema').innerHTML = `<div class="error">Error loading table: ${error.message}</div>`;
            }
        }

        async function executeQuery() {
            const query = document.getElementById('sqlQuery').value.trim();
            if (!query) {
                alert('Please enter a SQL query');
                return;
            }

            try {
                const response = await fetch('/api/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query })
                });

                const data = await response.json();
                const resultDiv = document.getElementById('queryResult');

                if (data.error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    return;
                }

                if (data.columns && data.rows) {
                    // SELECT query result
                    resultDiv.innerHTML = `
                        <h3>Query Results (${data.rows.length} rows)</h3>
                        <table class="data-table">
                            <thead>
                                <tr>${data.columns.map(col => `<th>${col}</th>`).join('')}</tr>
                            </thead>
                            <tbody>
                                ${data.rows.map(row => `
                                    <tr>${row.map(cell => `<td>${cell === null ? '<em>NULL</em>' : cell}</td>`).join('')}</tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                } else {
                    // Non-SELECT query result
                    resultDiv.innerHTML = `<div style="background: #f0fdf4; border: 1px solid #bbf7d0; color: #166534; padding: 1rem; border-radius: 6px; margin-top: 1rem;">
                        Query executed successfully. Rows affected: ${data.rowcount || 0}
                    </div>`;
                }

            } catch (error) {
                document.getElementById('queryResult').innerHTML = `<div class="error">Error executing query: ${error.message}</div>`;
            }
        }

        function clearQuery() {
            document.getElementById('sqlQuery').value = '';
            document.getElementById('queryResult').innerHTML = '';
        }

        // Load tables on page load
        loadTables();
    </script>
</body>
</html>
"""

def get_database_path():
    """Get the database file path"""
    possible_dbs = ['grace_medical.db', 'grace_medical_enhanced.db', 'database.db', 'app.db']
    
    for db_file in possible_dbs:
        if os.path.exists(db_file):
            return db_file
    
    return None

@app.route('/')
def index():
    """Main page"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/tables')
def get_tables():
    """Get all tables with metadata"""
    db_path = get_database_path()
    if not db_path:
        return jsonify({'error': 'No database file found'})
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name")
        table_names = [row[0] for row in cursor.fetchall()]
        
        tables = []
        total_rows = 0
        
        for table_name in table_names:
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            total_rows += row_count
            
            # Get column count
            cursor.execute(f"PRAGMA table_info({table_name})")
            column_count = len(cursor.fetchall())
            
            tables.append({
                'name': table_name,
                'row_count': row_count,
                'column_count': column_count
            })
        
        # Get database size
        db_size = os.path.getsize(db_path)
        db_size_str = f"{db_size:,} bytes"
        if db_size > 1024:
            db_size_str = f"{db_size/1024:.1f} KB"
        if db_size > 1024*1024:
            db_size_str = f"{db_size/(1024*1024):.1f} MB"
        
        conn.close()
        
        return jsonify({
            'tables': tables,
            'total_rows': total_rows,
            'db_size': db_size_str
        })
        
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/table/<table_name>')
def get_table_details(table_name):
    """Get table schema and sample data"""
    db_path = get_database_path()
    if not db_path:
        return jsonify({'error': 'No database file found'})
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute(f"PRAGMA table_info({table_name})")
        schema_rows = cursor.fetchall()
        
        schema = []
        for row in schema_rows:
            schema.append({
                'name': row[1],
                'type': row[2],
                'notnull': bool(row[3]),
                'default': row[4],
                'pk': bool(row[5])
            })
        
        # Get sample data (first 20 rows)
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 20")
        data = cursor.fetchall()
        
        conn.close()
        
        return jsonify({
            'schema': schema,
            'data': data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/query', methods=['POST'])
def execute_query():
    """Execute custom SQL query"""
    db_path = get_database_path()
    if not db_path:
        return jsonify({'error': 'No database file found'})
    
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({'error': 'No query provided'})
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute(query)
        
        if query.lower().strip().startswith('select'):
            # SELECT query - return results
            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description] if cursor.description else []
            
            conn.close()
            return jsonify({
                'columns': columns,
                'rows': rows
            })
        else:
            # Non-SELECT query - commit and return row count
            conn.commit()
            rowcount = cursor.rowcount
            conn.close()
            
            return jsonify({
                'rowcount': rowcount,
                'message': 'Query executed successfully'
            })
        
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    print("🗄️ Starting Grace Medical Limited Database Viewer...")
    print("📊 Open your browser and go to: http://localhost:5001")
    print("🔍 You can view all your database tables and run SQL queries")
    print("⏹️  Press Ctrl+C to stop the server")
    
    app.run(debug=True, host='0.0.0.0', port=5001)
