import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { ShoppingCart, Package, AlertTriangle, Clock, Star } from 'lucide-react'
import toast from 'react-hot-toast'

const ProductCard = ({ product, onAddToCart, viewMode = 'grid' }) => {
  const [imageLoading, setImageLoading] = useState(true)
  const [imageError, setImageError] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [imageSrc, setImageSrc] = useState('')

  // Enhanced image URL handling with multiple fallbacks
  const getImageUrl = (product) => {
    if (product.image_url && product.image_url.trim() !== '' && !product.image_url.includes('placeholder')) {
      return product.image_url
    }
    // Create a more specific placeholder based on product name
    const productName = product.name ? product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '+') : 'Medical+Supply'
    return `https://via.placeholder.com/300x200/e5e7eb/6b7280?text=${productName}`
  }

  // Set initial image source
  useEffect(() => {
    setImageSrc(getImageUrl(product))
    setImageLoading(true)
    setImageError(false)
  }, [product])

  const handleImageLoad = () => {
    setImageLoading(false)
    setImageError(false)
  }

  const handleImageError = (e) => {
    if (!imageError) {
      setImageError(true)
      setImageLoading(false)
      // Fallback to a generic medical supply placeholder
      const fallbackUrl = `https://via.placeholder.com/300x200/e5e7eb/6b7280?text=Medical+Supply`
      setImageSrc(fallbackUrl)
      e.target.src = fallbackUrl
    }
  }

  const handleAddToCart = async () => {
    if (product.stock_quantity === 0 || isAdding) return
    
    setIsAdding(true)
    try {
      await onAddToCart(product, 1)
      toast.success(`${product.name} added to cart!`, {
        duration: 2000,
        position: 'top-right',
        icon: '🛒',
      })
    } catch (error) {
      toast.error('Failed to add to cart')
    } finally {
      setIsAdding(false)
    }
  }

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
        <div className="flex">
          <Link to={`/products/${product.id}`} className="flex-shrink-0">
            <div className="w-48 h-32 bg-gray-100 relative overflow-hidden">
              {imageLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                </div>
              )}
              <img
                src={getImageUrl(product)}
                alt={product.name}
                className={`w-full h-full object-cover transition-all duration-300 hover:scale-105 ${
                  imageLoading ? 'opacity-0' : 'opacity-100'
                }`}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            </div>
          </Link>
          
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <Link to={`/products/${product.id}`}>
                  <h3 className="font-bold text-xl text-gray-900 mb-2 hover:text-primary-600 transition-colors">
                    {product.name}
                  </h3>
                </Link>
                
                <p className="text-gray-600 mb-3 line-clamp-2">
                  {product.description}
                </p>

                <div className="flex items-center gap-4 mb-3">
                  <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {product.category}
                  </span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {product.manufacturer}
                  </span>
                </div>
              </div>

              <div className="text-right ml-6">
                <div className="mb-3">
                  <div className="text-2xl font-bold text-primary-600">
                    {formatPrice(product.unit_price)}
                  </div>
                  {product.bulk_price && (
                    <div className="text-sm text-green-600 font-medium">
                      Bulk: {formatPrice(product.bulk_price)}
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-end gap-2 mb-4">
                  <Package className="h-4 w-4 text-gray-400" />
                  <span className={`text-sm font-medium ${
                    product.is_low_stock ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {product.stock_quantity} in stock
                  </span>
                </div>

                <button
                  onClick={handleAddToCart}
                  disabled={product.stock_quantity === 0 || isAdding}
                  className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                    product.stock_quantity === 0
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-primary-600 text-white hover:bg-primary-700 active:transform active:scale-95'
                  }`}
                >
                  {isAdding ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <ShoppingCart className="h-4 w-4" />
                  )}
                  {product.stock_quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <Link to={`/products/${product.id}`}>
        <div className="h-48 bg-gray-100 relative overflow-hidden">
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          )}
          <img
            src={imageSrc}
            alt={product.name}
            className={`w-full h-full object-cover transition-all duration-300 hover:scale-105 ${
              imageLoading ? 'opacity-0' : 'opacity-100'
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
            loading="lazy"
          />
          
          {/* Status badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {product.is_low_stock && (
              <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                Low Stock
              </span>
            )}
            {product.bulk_price && (
              <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                Bulk Available
              </span>
            )}
            {product.is_near_expiry && (
              <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                Near Expiry
              </span>
            )}
          </div>
        </div>
      </Link>
      
      <div className="p-6">
        <Link to={`/products/${product.id}`}>
          <h3 className="font-bold text-gray-900 mb-2 hover:text-primary-600 transition-colors text-lg leading-tight">
            {product.name}
          </h3>
        </Link>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2 leading-relaxed">
          {product.description}
        </p>

        <div className="flex items-center justify-between mb-3">
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            {product.category}
          </span>
          <div className="flex items-center gap-1">
            <Package className="h-4 w-4 text-gray-400" />
            <span className={`text-sm font-medium ${
              product.is_low_stock ? 'text-red-600' : 'text-green-600'
            }`}>
              {product.stock_quantity}
            </span>
          </div>
        </div>
        
        <div className="mb-4">
          <div className="flex items-baseline gap-2">
            <span className="text-2xl font-bold text-primary-600">
              {formatPrice(product.unit_price)}
            </span>
            <span className="text-sm text-gray-500">per unit</span>
          </div>
          {product.bulk_price && (
            <div className="flex items-baseline gap-2 mt-1">
              <span className="text-lg font-semibold text-green-600">
                {formatPrice(product.bulk_price)}
              </span>
              <span className="text-xs text-gray-500">bulk price (10+ units)</span>
            </div>
          )}
        </div>
        
        <button
          onClick={handleAddToCart}
          disabled={product.stock_quantity === 0 || isAdding}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
            product.stock_quantity === 0
              ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
              : 'bg-primary-600 text-white hover:bg-primary-700 active:transform active:scale-95'
          }`}
        >
          {isAdding ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          ) : (
            <ShoppingCart className="h-5 w-5" />
          )}
          {product.stock_quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
        </button>
      </div>
    </div>
  )
}

export default ProductCard
