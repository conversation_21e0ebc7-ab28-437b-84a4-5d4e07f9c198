# Grace Medical Limited - Setup Instructions

## Prerequisites

Before setting up the Grace Medical Limited surgical supply ordering system, ensure you have the following installed:

### Required Software
1. **Python 3.8+** - Download from [python.org](https://www.python.org/downloads/)
2. **Node.js 16+** - Download from [nodejs.org](https://nodejs.org/downloads/)
3. **Git** - Download from [git-scm.com](https://git-scm.com/downloads/)

### Verify Installation
Open a terminal/command prompt and run:
```bash
python --version    # Should show Python 3.8+
node --version      # Should show Node.js 16+
npm --version       # Should show npm 8+
```

## Backend Setup

### 1. Navigate to Backend Directory
```bash
cd backend
```

### 2. Create Virtual Environment (Recommended)
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
The `.env` file is already configured with default values. For production, update:
- `JWT_SECRET_KEY` - Use a secure random key
- `EMAIL_USER` and `EMAIL_PASSWORD` - Configure with your SMTP credentials
- `ADMIN_EMAIL` - Set your admin email address

### 5. Initialize Database
The database will be automatically created when you first run the application.
The CSV data will be imported automatically on first startup.

### 6. Start Backend Server
```bash
python app.py
```

The backend will be available at: `http://localhost:5000`

## Frontend Setup

### 1. Navigate to Frontend Directory
```bash
cd frontend
```

### 2. Install Node.js Dependencies
```bash
npm install
```

### 3. Start Development Server
```bash
npm run dev
```

The frontend will be available at: `http://localhost:3000`

## Default Admin Account

A default admin account is automatically created:
- **Email:** <EMAIL>
- **Password:** admin123

**⚠️ Important:** Change the admin password immediately after first login in production!

## Testing the Application

### 1. Access the Application
Open your browser and go to `http://localhost:3000`

### 2. Test User Registration
1. Click "Register" to create a new doctor account
2. Fill in the registration form
3. Note: Email verification is simulated (check console logs)

### 3. Test Admin Features
1. Login with the admin account
2. Access the admin dashboard at `/admin`
3. View products, orders, and reports

### 4. Test Product Ordering
1. Browse products at `/products`
2. Add items to cart
3. Complete checkout process
4. View order history

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/verify-email` - Email verification

### Products
- `GET /api/products` - Get all products (with pagination)
- `GET /api/products/{id}` - Get single product
- `GET /api/categories` - Get product categories

### Orders
- `GET /api/orders` - Get user orders
- `POST /api/orders` - Create new order
- `GET /api/orders/{id}` - Get order details

### Admin (Requires API Key)
- `GET /api/admin/orders` - Get all orders
- `PUT /api/admin/orders/{id}/status` - Update order status
- `GET /api/admin/low-stock` - Get low stock products
- `GET /api/admin/near-expiry` - Get near expiry products
- `GET /api/admin/reports` - Generate reports

## API Key Authentication

Admin routes require the API key in the `x-api-key` header:
```
x-api-key: 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
```

## Database Schema

### Users Table
- id, email, password_hash, first_name, last_name, phone
- is_admin, is_verified, verification_token, created_at

### Products Table
- id, name, description, packaging, unit_price, bulk_price
- manufacturer, expiry_date, stock_quantity, reorder_level
- category, image_url, is_active, created_at, updated_at

### Orders Table
- id, user_id, order_number, status, total_amount
- shipping_address, notes, created_at, updated_at

### Order Items Table
- id, order_id, product_id, quantity, unit_price, total_price

### Notifications Table
- id, user_id, title, message, type, is_read, created_at

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Backend: Change port in `app.py` (default: 5000)
   - Frontend: Change port in `vite.config.js` (default: 3000)

2. **Database Issues**
   - Delete `grace_medical.db` file and restart backend
   - Check CSV file format and location

3. **CORS Issues**
   - Ensure backend is running on port 5000
   - Check proxy configuration in `vite.config.js`

4. **CSV Import Issues**
   - Verify CSV file is in `backend/data/` directory
   - Check CSV format matches expected columns
   - Review console logs for import errors

### Development Tips

1. **Hot Reload**
   - Frontend: Automatically reloads on file changes
   - Backend: Restart manually after code changes

2. **Database Reset**
   - Delete `grace_medical.db` file to reset database
   - Products will be re-imported from CSV on next startup

3. **API Testing**
   - Use tools like Postman or curl to test API endpoints
   - Include proper headers for authentication

## Production Deployment

### Security Considerations
1. Change default admin password
2. Use secure JWT secret key
3. Configure proper SMTP settings
4. Use environment variables for sensitive data
5. Enable HTTPS
6. Use production database (PostgreSQL/MySQL)

### Environment Variables
Create production `.env` file with:
```
FLASK_ENV=production
FLASK_DEBUG=False
JWT_SECRET_KEY=your-secure-secret-key
DATABASE_URL=your-production-database-url
EMAIL_HOST=your-smtp-host
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
ADMIN_EMAIL=<EMAIL>
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review console logs for error messages
3. Verify all prerequisites are installed correctly
4. Ensure all services are running on correct ports

## Features Overview

### For Doctors
- ✅ Self-registration and login
- ✅ Browse product catalog with search/filter
- ✅ Add products to cart
- ✅ Place orders with shipping information
- ✅ Track order status
- ✅ View order history
- ✅ Mobile-responsive interface

### For Administrators
- ✅ Admin dashboard with statistics
- ✅ Manage product inventory
- ✅ View and update order status
- ✅ Monitor low stock items
- ✅ Track near-expiry products
- ✅ Generate reports
- ✅ Email notifications for new orders

### Technical Features
- ✅ JWT authentication
- ✅ API key protection for admin routes
- ✅ Automatic CSV data import
- ✅ Responsive design with Tailwind CSS
- ✅ Real-time notifications
- ✅ Order status tracking
- ✅ Bulk pricing support
- ✅ Stock management
- ✅ Expiry date monitoring
