#!/usr/bin/env python3
"""
Database Schema Fix for Grace Medical Limited
Adds missing phone column to orders table and fixes image URLs
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database(db_path):
    """Create a backup of the existing database"""
    if os.path.exists(db_path):
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    return None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    return column_name in column_names

def fix_orders_table(cursor):
    """Add missing phone column to orders table if it doesn't exist"""
    print("🔍 Checking orders table schema...")
    
    if not check_column_exists(cursor, 'orders', 'phone'):
        print("📞 Adding phone column to orders table...")
        cursor.execute('ALTER TABLE orders ADD COLUMN phone TEXT')
        print("✅ Phone column added successfully")
    else:
        print("✅ Phone column already exists")
    
    # Also ensure email column exists for better order management
    if not check_column_exists(cursor, 'orders', 'email'):
        print("📧 Adding email column to orders table...")
        cursor.execute('ALTER TABLE orders ADD COLUMN email TEXT')
        print("✅ Email column added successfully")
    else:
        print("✅ Email column already exists")

def fix_product_images(cursor):
    """Fix product image URLs - add placeholder for missing images"""
    print("🖼️ Fixing product image URLs...")
    
    # Get products with missing or empty image URLs
    cursor.execute('''
        SELECT id, name, image_url FROM products 
        WHERE image_url IS NULL OR image_url = '' OR image_url = 'null'
    ''')
    
    products_to_fix = cursor.fetchall()
    
    if products_to_fix:
        print(f"🔧 Found {len(products_to_fix)} products with missing images")
        
        for product_id, product_name, current_url in products_to_fix:
            # Create a placeholder image URL based on product name
            safe_name = product_name.replace(' ', '+').replace('&', 'and')
            placeholder_url = f"https://via.placeholder.com/300x200/e5e7eb/6b7280?text={safe_name}"
            
            cursor.execute('''
                UPDATE products 
                SET image_url = ? 
                WHERE id = ?
            ''', (placeholder_url, product_id))
            
            print(f"  ✅ Fixed image for: {product_name}")
    else:
        print("✅ All products already have image URLs")

def verify_schema(cursor):
    """Verify the database schema is correct"""
    print("\n🔍 Verifying database schema...")
    
    # Check orders table
    cursor.execute("PRAGMA table_info(orders)")
    orders_columns = [col[1] for col in cursor.fetchall()]
    
    required_orders_columns = ['id', 'user_id', 'order_number', 'status', 'total_amount', 
                              'shipping_address', 'phone', 'email', 'created_at']
    
    missing_orders_columns = [col for col in required_orders_columns if col not in orders_columns]
    
    if missing_orders_columns:
        print(f"❌ Missing columns in orders table: {missing_orders_columns}")
        return False
    else:
        print("✅ Orders table schema is correct")
    
    # Check products table
    cursor.execute("PRAGMA table_info(products)")
    products_columns = [col[1] for col in cursor.fetchall()]
    
    if 'image_url' not in products_columns:
        print("❌ Missing image_url column in products table")
        return False
    else:
        print("✅ Products table schema is correct")
    
    # Check for products without images
    cursor.execute('''
        SELECT COUNT(*) FROM products 
        WHERE image_url IS NULL OR image_url = '' OR image_url = 'null'
    ''')
    
    missing_images_count = cursor.fetchone()[0]
    if missing_images_count > 0:
        print(f"⚠️ {missing_images_count} products still have missing images")
    else:
        print("✅ All products have image URLs")
    
    return True

def main():
    """Main function to fix database schema"""
    print("🔧 Grace Medical Limited - Database Schema Fix")
    print("=" * 50)
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), 'grace_medical.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        print("Please make sure you're running this from the backend directory")
        return False
    
    # Create backup
    backup_path = backup_database(db_path)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Fix orders table
        fix_orders_table(cursor)
        
        # Fix product images
        fix_product_images(cursor)
        
        # Commit changes
        conn.commit()
        
        # Verify schema
        if verify_schema(cursor):
            print("\n🎉 Database schema fix completed successfully!")
            print("\n📋 Summary:")
            print("✅ Phone column added to orders table")
            print("✅ Email column added to orders table") 
            print("✅ Product image URLs fixed with placeholders")
            print("✅ Database schema verified")
            
            print("\n🚀 Next steps:")
            print("1. Restart your backend server")
            print("2. Test order placement - phone column error should be fixed")
            print("3. Check product cards - images should now appear")
            
            return True
        else:
            print("\n❌ Schema verification failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Error fixing database: {e}")
        
        # Restore backup if something went wrong
        if backup_path and os.path.exists(backup_path):
            print(f"🔄 Restoring backup from: {backup_path}")
            shutil.copy2(backup_path, db_path)
            print("✅ Database restored from backup")
        
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 If issues persist:")
        print("1. Check that the backend server is stopped before running this script")
        print("2. Make sure you have write permissions to the database file")
        print("3. Try deleting the database file and restarting the server to recreate it")
        exit(1)
    else:
        print("\n✅ Database fix completed successfully!")
        exit(0)
