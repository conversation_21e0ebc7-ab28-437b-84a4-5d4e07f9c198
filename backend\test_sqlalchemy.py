"""
Test script for Grace Medical Limited SQLAlchemy implementation
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app_sqlalchemy import app, db
from database import User, Product, Order, OrderItem, Category

def test_database_setup():
    """Test basic database setup and operations"""
    
    with app.app_context():
        print("🧪 Testing SQLAlchemy Database Setup")
        print("=" * 50)
        
        # Test 1: Database connection
        try:
            db.create_all()
            print("✅ Database tables created successfully")
        except Exception as e:
            print(f"❌ Database creation failed: {e}")
            return False
        
        # Test 2: User operations
        try:
            # Create test user
            test_user = User(
                email='<EMAIL>',
                first_name='Test',
                last_name='User',
                is_verified=True
            )
            test_user.set_password('testpass123')
            
            db.session.add(test_user)
            db.session.commit()
            
            # Query user
            found_user = User.query.filter_by(email='<EMAIL>').first()
            assert found_user is not None
            assert found_user.check_password('testpass123')
            
            print("✅ User operations working")
            
        except Exception as e:
            print(f"❌ User operations failed: {e}")
            db.session.rollback()
            return False
        
        # Test 3: Product operations
        try:
            # Create test product
            test_product = Product(
                name='Test Medical Device',
                description='A test medical device',
                category='Testing',
                manufacturer='Test Corp',
                unit_price=99.99,
                stock_quantity=50,
                reorder_level=10
            )
            
            db.session.add(test_product)
            db.session.commit()
            
            # Query product
            found_product = Product.query.filter_by(name='Test Medical Device').first()
            assert found_product is not None
            assert found_product.is_low_stock == False  # 50 > 10
            
            print("✅ Product operations working")
            
        except Exception as e:
            print(f"❌ Product operations failed: {e}")
            db.session.rollback()
            return False
        
        # Test 4: Order operations
        try:
            # Create test order
            test_order = Order(
                order_number='TEST-001',
                user_id=test_user.id,
                status='Pending',
                total_amount=99.99,
                shipping_address='123 Test St, Test City',
                phone='************'
            )
            
            db.session.add(test_order)
            db.session.flush()
            
            # Create order item
            test_order_item = OrderItem(
                order_id=test_order.id,
                product_id=test_product.id,
                quantity=1,
                unit_price=99.99,
                total_price=99.99
            )
            
            db.session.add(test_order_item)
            db.session.commit()
            
            # Query order with items
            found_order = Order.query.filter_by(order_number='TEST-001').first()
            assert found_order is not None
            assert len(found_order.order_items) == 1
            
            print("✅ Order operations working")
            
        except Exception as e:
            print(f"❌ Order operations failed: {e}")
            db.session.rollback()
            return False
        
        # Test 5: Relationships
        try:
            # Test user -> orders relationship
            user_orders = test_user.orders
            assert len(user_orders) == 1
            
            # Test order -> user relationship
            order_user = test_order.user
            assert order_user.email == '<EMAIL>'
            
            # Test order_item -> product relationship
            item_product = test_order_item.product
            assert item_product.name == 'Test Medical Device'
            
            print("✅ Database relationships working")
            
        except Exception as e:
            print(f"❌ Relationship tests failed: {e}")
            return False
        
        # Test 6: Model methods
        try:
            # Test to_dict methods
            user_dict = test_user.to_dict()
            assert 'email' in user_dict
            assert 'password_hash' not in user_dict  # Should not expose password
            
            product_dict = test_product.to_dict()
            assert 'name' in product_dict
            assert 'is_low_stock' in product_dict
            
            order_dict = test_order.to_dict(include_items=True)
            assert 'order_number' in order_dict
            assert 'items' in order_dict
            assert len(order_dict['items']) == 1
            
            print("✅ Model methods working")
            
        except Exception as e:
            print(f"❌ Model method tests failed: {e}")
            return False
        
        # Cleanup test data
        try:
            db.session.delete(test_order_item)
            db.session.delete(test_order)
            db.session.delete(test_product)
            db.session.delete(test_user)
            db.session.commit()
            print("✅ Test cleanup completed")
            
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
        
        print("=" * 50)
        print("🎉 All SQLAlchemy tests passed!")
        return True

def test_api_endpoints():
    """Test API endpoints"""
    
    print("\n🌐 Testing API Endpoints")
    print("=" * 50)
    
    with app.test_client() as client:
        # Test products endpoint
        response = client.get('/api/products')
        assert response.status_code == 200
        data = response.get_json()
        assert 'products' in data
        print("✅ Products API working")
        
        # Test categories endpoint
        response = client.get('/api/categories')
        assert response.status_code == 200
        data = response.get_json()
        assert isinstance(data, list)
        print("✅ Categories API working")
        
        # Test registration endpoint
        test_user_data = {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'API',
            'last_name': 'Test'
        }
        
        response = client.post('/api/auth/register', json=test_user_data)
        assert response.status_code == 201
        print("✅ Registration API working")
        
        # Test login endpoint
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = client.post('/api/auth/login', json=login_data)
        assert response.status_code == 200
        data = response.get_json()
        assert 'access_token' in data
        assert 'user' in data
        print("✅ Login API working")
        
        print("=" * 50)
        print("🎉 All API tests passed!")

if __name__ == '__main__':
    print("🧪 Grace Medical Limited - SQLAlchemy Test Suite")
    print("=" * 60)
    
    # Run database tests
    if test_database_setup():
        # Run API tests
        test_api_endpoints()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("🚀 SQLAlchemy implementation is ready for use!")
    else:
        print("\n" + "=" * 60)
        print("❌ TESTS FAILED!")
        print("🔧 Please check the database setup and try again.")
        sys.exit(1)
