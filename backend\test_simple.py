#!/usr/bin/env python3
"""
Simple test to check if the Flask app can be imported and run
"""

print("Testing Flask app...")

try:
    print("1. Testing import...")
    import app
    print("✓ App imported successfully!")
    
    print("2. Testing app creation...")
    flask_app = app.app
    print(f"✓ Flask app created: {flask_app}")
    
    print("3. Testing configuration...")
    print(f"✓ JWT Available: {app.JWT_AVAILABLE}")
    print(f"✓ CORS Available: {app.CORS_AVAILABLE}")
    print(f"✓ API Key set: {'Yes' if app.API_KEY else 'No'}")
    
    print("4. Testing mock data...")
    print(f"✓ Products: {len(app.MOCK_PRODUCTS)}")
    print(f"✓ Users: {len(app.MOCK_USERS)}")
    print(f"✓ Orders: {len(app.MOCK_ORDERS)}")
    
    print("5. Testing routes...")
    with flask_app.test_client() as client:
        # Test home route
        response = client.get('/')
        print(f"✓ Home route: {response.status_code}")
        
        # Test health check
        response = client.get('/api/health')
        print(f"✓ Health check: {response.status_code}")
        
        # Test products route
        response = client.get('/api/products')
        print(f"✓ Products route: {response.status_code}")
    
    print("\n🎉 All tests passed! The Flask app is working correctly.")
    print("\nTo start the server manually, run:")
    print("python app.py")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
