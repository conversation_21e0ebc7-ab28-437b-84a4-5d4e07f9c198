from functools import wraps
from flask import request, jsonify, current_app
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity
from models import User, db
import os

def require_api_key(f):
    """Decorator to require API key for admin routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('x-api-key')
        expected_key = os.getenv('API_KEY')
        
        if not api_key or api_key != expected_key:
            return jsonify({'error': 'Invalid or missing API key'}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def require_auth(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
            current_user = User.query.get(current_user_id)
            
            if not current_user:
                return jsonify({'error': 'User not found'}), 404
            
            if not current_user.is_verified:
                return jsonify({'error': 'Email not verified'}), 403
            
            return f(current_user, *args, **kwargs)
        except Exception as e:
            return jsonify({'error': 'Invalid token'}), 401
    
    return decorated_function

def require_admin(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
            current_user = User.query.get(current_user_id)
            
            if not current_user:
                return jsonify({'error': 'User not found'}), 404
            
            if not current_user.is_admin:
                return jsonify({'error': 'Admin privileges required'}), 403
            
            return f(current_user, *args, **kwargs)
        except Exception as e:
            return jsonify({'error': 'Invalid token'}), 401
    
    return decorated_function

def send_verification_email(user):
    """Send email verification (placeholder implementation)"""
    # In a real implementation, you would use a proper email service
    print(f"Verification email would be sent to {user.email}")
    print(f"Verification token: {user.verification_token}")
    return True

def send_order_notification_email(order):
    """Send order notification email to admin"""
    # In a real implementation, you would use a proper email service
    admin_email = os.getenv('ADMIN_EMAIL')
    print(f"Order notification would be sent to {admin_email}")
    print(f"New order: {order.order_number} - Total: ${order.total_amount}")
    return True
