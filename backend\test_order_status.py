#!/usr/bin/env python3
"""
Test script for the enhanced order status management system
"""

import sqlite3
import os
from datetime import datetime, timedelta

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def create_test_order():
    """Create a test order to demonstrate the status management system"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get the first user (admin user)
        cursor.execute('SELECT id FROM users LIMIT 1')
        user_result = cursor.fetchone()
        
        if not user_result:
            print("❌ No users found. Please create a user first.")
            return None
            
        user_id = user_result[0]
        
        # Create a test order
        order_number = f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        cursor.execute('''
            INSERT INTO orders (
                user_id, order_number, status, total_amount, 
                shipping_address, billing_address, phone, notes,
                created_at, updated_at, status_updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id, order_number, 'Pending', 125.50,
            '123 Test Street, Test City, TC 12345',
            '123 Test Street, Test City, TC 12345',
            '******-123-4567',
            'Test order for status management demonstration',
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        order_id = cursor.lastrowid
        
        # Add some test order items
        test_items = [
            (1, 2, 25.99, 51.98),  # 2x Disposable Syringes 10ml
            (2, 1, 18.50, 18.50),  # 1x Nitrile Examination Gloves
            (3, 3, 12.99, 38.97),  # 3x Surgical Face Masks
            (4, 1, 45.00, 45.00)   # 1x Digital Thermometer
        ]
        
        for product_id, quantity, unit_price, total_price in test_items:
            cursor.execute('''
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (order_id, product_id, quantity, unit_price, total_price))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Test order created successfully!")
        print(f"   Order ID: {order_id}")
        print(f"   Order Number: {order_number}")
        print(f"   Status: Pending")
        print(f"   Total: $125.50")
        print(f"   Items: 4 different medical products")
        
        return order_id
        
    except Exception as e:
        print(f"❌ Error creating test order: {e}")
        return None

def test_status_updates():
    """Test the order status update functionality"""
    try:
        from order_status_manager import order_status_manager
        
        # Create a test order
        order_id = create_test_order()
        if not order_id:
            return
        
        print(f"\n🔄 Testing order status updates for Order ID: {order_id}")
        print("=" * 60)
        
        # Test status progression
        statuses = ['Processing', 'Shipped', 'Complete']
        
        for i, status in enumerate(statuses):
            print(f"\n{i+1}. Updating status to: {status}")
            
            # Add tracking number for shipped status
            tracking_number = f"TRACK{order_id}123" if status == 'Shipped' else None
            
            success = order_status_manager.update_order_status(
                order_id=order_id,
                new_status=status,
                changed_by='test_system',
                change_reason=f'Test update to {status}',
                tracking_number=tracking_number
            )
            
            if success:
                print(f"   ✅ Successfully updated to {status}")
                if tracking_number:
                    print(f"   📦 Tracking number: {tracking_number}")
            else:
                print(f"   ❌ Failed to update to {status}")
        
        # Show status history
        print(f"\n📈 Order Status History:")
        print("-" * 40)
        
        history = order_status_manager.get_order_status_history(order_id)
        for change in history:
            print(f"   {change['created_at']}")
            print(f"   {change['old_status'] or 'New'} → {change['new_status']}")
            print(f"   Changed by: {change['changed_by']}")
            if change['change_reason']:
                print(f"   Reason: {change['change_reason']}")
            print()
        
        print("✅ Order status management test completed!")
        
    except ImportError:
        print("❌ Order status manager not available")
    except Exception as e:
        print(f"❌ Error testing status updates: {e}")

def show_current_orders():
    """Show all current orders in the database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, order_number, status, total_amount, created_at, status_updated_at
            FROM orders
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        orders = cursor.fetchall()
        conn.close()
        
        if not orders:
            print("📦 No orders found in the database")
            return
        
        print(f"\n📋 Current Orders (showing last 10):")
        print("=" * 80)
        print(f"{'ID':<4} {'Order Number':<20} {'Status':<12} {'Total':<10} {'Created':<20} {'Updated':<20}")
        print("-" * 80)
        
        for order in orders:
            order_id, order_number, status, total_amount, created_at, status_updated_at = order
            created_date = datetime.fromisoformat(created_at).strftime('%Y-%m-%d %H:%M') if created_at else 'N/A'
            updated_date = datetime.fromisoformat(status_updated_at).strftime('%Y-%m-%d %H:%M') if status_updated_at else 'N/A'
            
            print(f"{order_id:<4} {order_number:<20} {status:<12} ${total_amount:<9.2f} {created_date:<20} {updated_date:<20}")
        
    except Exception as e:
        print(f"❌ Error showing orders: {e}")

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Order Status Management Test")
    print("=" * 60)
    
    # Show current orders
    show_current_orders()
    
    # Test the order status management system
    test_status_updates()
    
    # Show orders again to see the changes
    show_current_orders()
    
    print("\n🎉 Test completed! Check the 'My Orders' section in the web app to see the enhanced status display.")
