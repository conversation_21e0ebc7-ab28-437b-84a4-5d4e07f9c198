#!/usr/bin/env python3
"""
Fix admin user password hash
"""

import sqlite3
import os
from werkzeug.security import generate_password_hash, check_password_hash

DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def fix_admin_user():
    """Fix the admin user password hash"""
    print("🔧 Fixing Admin User Password")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check current admin user
        cursor.execute('SELECT id, email, password_hash FROM users WHERE email = ?', ('<EMAIL>',))
        admin = cursor.fetchone()
        
        if admin:
            print(f"✅ Found admin user: {admin[1]}")
            
            # Test current password
            current_valid = check_password_hash(admin[2], 'admin123')
            print(f"   Current password valid: {current_valid}")
            
            if not current_valid:
                print("🔄 Updating admin password hash...")
                
                # Generate new password hash
                new_hash = generate_password_hash('admin123')
                
                # Update the admin user
                cursor.execute('''
                    UPDATE users 
                    SET password_hash = ?, is_verified = 1, is_admin = 1
                    WHERE email = ?
                ''', (new_hash, '<EMAIL>'))
                
                conn.commit()
                
                # Verify the fix
                cursor.execute('SELECT password_hash FROM users WHERE email = ?', ('<EMAIL>',))
                updated_hash = cursor.fetchone()[0]
                
                test_valid = check_password_hash(updated_hash, 'admin123')
                print(f"   Updated password valid: {test_valid}")
                
                if test_valid:
                    print("✅ Admin password fixed successfully!")
                else:
                    print("❌ Failed to fix admin password")
                    return False
            else:
                print("✅ Admin password is already correct")
        else:
            print("❌ Admin user not found, creating new one...")
            
            # Create new admin user
            password_hash = generate_password_hash('admin123')
            cursor.execute('''
                INSERT INTO users (email, password_hash, first_name, last_name, is_admin, is_verified)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                '<EMAIL>',
                password_hash,
                'Admin',
                'User',
                True,
                True
            ))
            conn.commit()
            print("✅ New admin user created!")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing admin user: {e}")
        return False

def test_admin_login_api():
    """Test admin login via API"""
    print("\n🌐 Testing Admin Login API")
    print("=" * 40)
    
    try:
        import requests
        
        response = requests.post('http://127.0.0.1:5000/api/auth/login', 
                               json={
                                   'email': '<EMAIL>',
                                   'password': 'admin123'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Admin login successful!")
            print(f"   User: {data['user']['first_name']} {data['user']['last_name']}")
            print(f"   Email: {data['user']['email']}")
            print(f"   Is Admin: {data['user']['is_admin']}")
            print(f"   Token: {data['access_token'][:30]}...")
            return True
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Admin User Fix")
    print("=" * 50)
    
    # Fix admin user
    fix_success = fix_admin_user()
    
    if fix_success:
        # Test the fix
        api_success = test_admin_login_api()
        
        if api_success:
            print(f"\n🎉 Admin login is now working correctly!")
            print(f"   Email: <EMAIL>")
            print(f"   Password: admin123")
        else:
            print(f"\n⚠️ Admin user fixed but API test failed")
    else:
        print(f"\n❌ Failed to fix admin user")
