#!/usr/bin/env python3
"""
Order Status Scheduler for Grace Medical Limited
Runs automatic order status updates in the background
"""

import time
import threading
from datetime import datetime
import schedule

try:
    from order_status_manager import order_status_manager
    ORDER_STATUS_MANAGER_AVAILABLE = True
except ImportError:
    ORDER_STATUS_MANAGER_AVAILABLE = False
    print("⚠️ Order status manager not available for scheduler")

class OrderScheduler:
    def __init__(self):
        self.running = False
        self.thread = None
        
    def start(self):
        """Start the background scheduler"""
        if not ORDER_STATUS_MANAGER_AVAILABLE:
            print("❌ Cannot start scheduler: Order status manager not available")
            return False
            
        if self.running:
            print("⚠️ Scheduler is already running")
            return False
            
        self.running = True
        
        # Schedule automatic status updates
        schedule.every(30).minutes.do(self._run_auto_updates)  # Every 30 minutes
        schedule.every().hour.do(self._log_status)  # Log status every hour
        
        # Start the scheduler thread
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        
        print("✅ Order status scheduler started")
        print("   - Auto updates: Every 30 minutes")
        print("   - Status logging: Every hour")
        return True
    
    def stop(self):
        """Stop the background scheduler"""
        self.running = False
        schedule.clear()
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            
        print("🛑 Order status scheduler stopped")
    
    def _run_scheduler(self):
        """Main scheduler loop"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                print(f"❌ Scheduler error: {e}")
                time.sleep(60)
    
    def _run_auto_updates(self):
        """Run automatic status updates"""
        try:
            print(f"🔄 Running automatic order status updates at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            order_status_manager.run_auto_status_updates()
            print("✅ Automatic status updates completed")
        except Exception as e:
            print(f"❌ Error in automatic status updates: {e}")
    
    def _log_status(self):
        """Log scheduler status"""
        print(f"📊 Order scheduler status check at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   - Scheduler running: {self.running}")
        print(f"   - Thread alive: {self.thread.is_alive() if self.thread else False}")

# Global scheduler instance
order_scheduler = OrderScheduler()

def start_order_scheduler():
    """Start the order status scheduler"""
    return order_scheduler.start()

def stop_order_scheduler():
    """Stop the order status scheduler"""
    order_scheduler.stop()

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Order Status Scheduler")
    print("=" * 50)
    
    if start_order_scheduler():
        try:
            print("Press Ctrl+C to stop the scheduler...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping scheduler...")
            stop_order_scheduler()
            print("👋 Scheduler stopped. Goodbye!")
    else:
        print("❌ Failed to start scheduler")
