#!/usr/bin/env python3
"""
Order Status Management System for Grace Medical Limited
Handles automatic order status transitions, manual updates, and email notifications
"""

import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

# Order status flow configuration
ORDER_STATUS_FLOW = {
    'Pending': {
        'next': 'Processing',
        'auto_transition_hours': 2,  # Auto transition after 2 hours
        'color': '#6B7280',  # Grey
        'description': 'Order received and awaiting processing'
    },
    'Processing': {
        'next': 'Shipped',
        'auto_transition_hours': 24,  # Auto transition after 24 hours
        'color': '#3B82F6',  # Blue
        'description': 'Order is being prepared and processed'
    },
    'Shipped': {
        'next': 'Complete',
        'auto_transition_hours': 72,  # Auto transition after 72 hours (3 days)
        'color': '#10B981',  # Green
        'description': 'Order has been shipped and is in transit'
    },
    'Complete': {
        'next': None,
        'auto_transition_hours': None,
        'color': '#059669',  # Dark <PERSON>
        'description': 'Order has been delivered and completed'
    },
    'Cancelled': {
        'next': None,
        'auto_transition_hours': None,
        'color': '#EF4444',  # Red
        'description': 'Order has been cancelled'
    }
}

# Email configuration
EMAIL_CONFIG = {
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'admin_email': '<EMAIL>',
    'admin_password': 'your_app_password',  # Use app password for Gmail
    'company_name': 'Grace Medical Limited'
}

class OrderStatusManager:
    def __init__(self):
        self.db_path = DB_PATH
        self.email_config = EMAIL_CONFIG
        self.init_status_tracking_table()
    
    def init_status_tracking_table(self):
        """Initialize order status tracking table"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create order status history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS order_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id INTEGER NOT NULL,
                    old_status TEXT,
                    new_status TEXT NOT NULL,
                    changed_by TEXT DEFAULT 'system',
                    change_reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
            ''')
            
            # Add status_updated_at column to orders table if it doesn't exist
            cursor.execute("PRAGMA table_info(orders)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'status_updated_at' not in columns:
                cursor.execute('ALTER TABLE orders ADD COLUMN status_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            
            if 'estimated_delivery' not in columns:
                cursor.execute('ALTER TABLE orders ADD COLUMN estimated_delivery TIMESTAMP')
            
            if 'tracking_number' not in columns:
                cursor.execute('ALTER TABLE orders ADD COLUMN tracking_number TEXT')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error initializing status tracking table: {e}")
    
    def update_order_status(self, order_id: int, new_status: str, changed_by: str = 'system', 
                          change_reason: str = None, tracking_number: str = None) -> bool:
        """Update order status and record history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get current order details
            cursor.execute('''
                SELECT id, status, user_id, order_number, total_amount, created_at
                FROM orders WHERE id = ?
            ''', (order_id,))
            
            order = cursor.fetchone()
            if not order:
                return False
            
            old_status = order[1]
            user_id = order[2]
            order_number = order[3]
            total_amount = order[4]
            
            # Validate status transition
            if new_status not in ORDER_STATUS_FLOW:
                return False
            
            # Update order status
            update_query = '''
                UPDATE orders 
                SET status = ?, status_updated_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            '''
            params = [new_status, order_id]
            
            # Add tracking number if provided
            if tracking_number:
                update_query = update_query.replace('updated_at = CURRENT_TIMESTAMP', 
                                                  'tracking_number = ?, updated_at = CURRENT_TIMESTAMP')
                params.insert(-1, tracking_number)
            
            # Set estimated delivery for shipped orders
            if new_status == 'Shipped':
                estimated_delivery = datetime.now() + timedelta(days=3)
                update_query = update_query.replace('updated_at = CURRENT_TIMESTAMP', 
                                                  'estimated_delivery = ?, updated_at = CURRENT_TIMESTAMP')
                params.insert(-1, estimated_delivery.isoformat())
            
            update_query += ' WHERE id = ?'
            cursor.execute(update_query, params)
            
            # Record status change history
            cursor.execute('''
                INSERT INTO order_status_history 
                (order_id, old_status, new_status, changed_by, change_reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (order_id, old_status, new_status, changed_by, change_reason))
            
            conn.commit()
            
            # Get user email for notifications
            cursor.execute('SELECT email, first_name, last_name FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()
            
            conn.close()
            
            # Send email notifications
            if user:
                user_email = user[0]
                user_name = f"{user[1]} {user[2]}"
                
                # Send notification to customer
                self.send_customer_notification(user_email, user_name, order_number, 
                                              old_status, new_status, total_amount, tracking_number)
                
                # Send notification to admin
                self.send_admin_notification(order_id, order_number, old_status, 
                                           new_status, user_email, changed_by, change_reason)
            
            return True
            
        except Exception as e:
            print(f"Error updating order status: {e}")
            return False
    
    def get_order_status_history(self, order_id: int) -> List[Dict]:
        """Get order status change history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT old_status, new_status, changed_by, change_reason, created_at
                FROM order_status_history
                WHERE order_id = ?
                ORDER BY created_at ASC
            ''', (order_id,))
            
            history = []
            for row in cursor.fetchall():
                history.append({
                    'old_status': row[0],
                    'new_status': row[1],
                    'changed_by': row[2],
                    'change_reason': row[3],
                    'created_at': row[4]
                })
            
            conn.close()
            return history
            
        except Exception as e:
            print(f"Error getting order status history: {e}")
            return []
    
    def get_orders_for_auto_transition(self) -> List[Tuple]:
        """Get orders that are ready for automatic status transition"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            orders_to_update = []
            
            for status, config in ORDER_STATUS_FLOW.items():
                if config['next'] and config['auto_transition_hours']:
                    # Calculate cutoff time
                    cutoff_time = datetime.now() - timedelta(hours=config['auto_transition_hours'])
                    
                    cursor.execute('''
                        SELECT id, order_number, status_updated_at
                        FROM orders
                        WHERE status = ? AND status_updated_at <= ?
                    ''', (status, cutoff_time.isoformat()))
                    
                    for row in cursor.fetchall():
                        orders_to_update.append((row[0], status, config['next']))
            
            conn.close()
            return orders_to_update
            
        except Exception as e:
            print(f"Error getting orders for auto transition: {e}")
            return []

    def send_customer_notification(self, customer_email: str, customer_name: str,
                                 order_number: str, old_status: str, new_status: str,
                                 total_amount: float, tracking_number: str = None):
        """Send order status update notification to customer"""
        try:
            status_config = ORDER_STATUS_FLOW.get(new_status, {})

            subject = f"Order Update - {order_number} - {new_status}"

            # Create HTML email content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #2563eb; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9fafb; }}
                    .status-badge {{
                        display: inline-block;
                        padding: 8px 16px;
                        border-radius: 20px;
                        color: white;
                        font-weight: bold;
                        background-color: {status_config.get('color', '#6B7280')};
                    }}
                    .order-details {{ background-color: white; padding: 15px; border-radius: 8px; margin: 15px 0; }}
                    .footer {{ text-align: center; padding: 20px; color: #6b7280; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🏥 Grace Medical Limited</h1>
                        <p>Order Status Update</p>
                    </div>

                    <div class="content">
                        <h2>Hello {customer_name},</h2>

                        <p>Your order status has been updated:</p>

                        <div class="order-details">
                            <h3>Order #{order_number}</h3>
                            <p><strong>Previous Status:</strong> {old_status}</p>
                            <p><strong>Current Status:</strong> <span class="status-badge">{new_status}</span></p>
                            <p><strong>Description:</strong> {status_config.get('description', '')}</p>
                            <p><strong>Order Total:</strong> ${total_amount:.2f}</p>
                            {f'<p><strong>Tracking Number:</strong> {tracking_number}</p>' if tracking_number else ''}
                        </div>

                        {self._get_status_timeline_html(new_status)}

                        <p>Thank you for choosing Grace Medical Limited for your medical supply needs.</p>

                        <p>If you have any questions about your order, please don't hesitate to contact us.</p>
                    </div>

                    <div class="footer">
                        <p>Grace Medical Limited<br>
                        Email: <EMAIL><br>
                        Phone: +****************</p>
                    </div>
                </div>
            </body>
            </html>
            """

            self._send_email(customer_email, subject, html_content)

        except Exception as e:
            print(f"Error sending customer notification: {e}")

    def send_admin_notification(self, order_id: int, order_number: str, old_status: str,
                              new_status: str, customer_email: str, changed_by: str,
                              change_reason: str = None):
        """Send order status update notification to admin"""
        try:
            subject = f"Admin Alert - Order {order_number} Status Changed to {new_status}"

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #dc2626; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9fafb; }}
                    .alert-box {{ background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 15px; margin: 15px 0; }}
                    .order-details {{ background-color: white; padding: 15px; border-radius: 8px; margin: 15px 0; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🏥 Grace Medical Admin</h1>
                        <p>Order Status Update Alert</p>
                    </div>

                    <div class="content">
                        <div class="alert-box">
                            <h3>Order Status Changed</h3>
                            <p>Order #{order_number} status has been updated.</p>
                        </div>

                        <div class="order-details">
                            <h3>Order Details</h3>
                            <p><strong>Order ID:</strong> {order_id}</p>
                            <p><strong>Order Number:</strong> {order_number}</p>
                            <p><strong>Customer Email:</strong> {customer_email}</p>
                            <p><strong>Previous Status:</strong> {old_status}</p>
                            <p><strong>New Status:</strong> {new_status}</p>
                            <p><strong>Changed By:</strong> {changed_by}</p>
                            {f'<p><strong>Reason:</strong> {change_reason}</p>' if change_reason else ''}
                            <p><strong>Timestamp:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                        </div>

                        <p>Please review the order status change and take any necessary actions.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            self._send_email(self.email_config['admin_email'], subject, html_content)

        except Exception as e:
            print(f"Error sending admin notification: {e}")

    def _get_status_timeline_html(self, current_status: str) -> str:
        """Generate HTML for order status timeline"""
        statuses = ['Pending', 'Processing', 'Shipped', 'Complete']
        current_index = statuses.index(current_status) if current_status in statuses else 0

        timeline_html = '<div style="margin: 20px 0;"><h4>Order Progress:</h4><div style="display: flex; justify-content: space-between; align-items: center;">'

        for i, status in enumerate(statuses):
            color = ORDER_STATUS_FLOW[status]['color'] if i <= current_index else '#E5E7EB'
            timeline_html += f'''
                <div style="text-align: center; flex: 1;">
                    <div style="width: 20px; height: 20px; border-radius: 50%; background-color: {color}; margin: 0 auto;"></div>
                    <small style="color: {color};">{status}</small>
                </div>
            '''
            if i < len(statuses) - 1:
                line_color = ORDER_STATUS_FLOW[status]['color'] if i < current_index else '#E5E7EB'
                timeline_html += f'<div style="flex: 1; height: 2px; background-color: {line_color}; margin: 0 5px;"></div>'

        timeline_html += '</div></div>'
        return timeline_html

    def _send_email(self, to_email: str, subject: str, html_content: str):
        """Send email using SMTP"""
        try:
            # For development, just log the email instead of actually sending
            print(f"EMAIL NOTIFICATION:")
            print(f"To: {to_email}")
            print(f"Subject: {subject}")
            print(f"Content: {html_content[:200]}...")
            print("-" * 50)

            # Uncomment below for actual email sending
            """
            msg = MimeMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.email_config['admin_email']
            msg['To'] = to_email

            html_part = MimeText(html_content, 'html')
            msg.attach(html_part)

            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['admin_email'], self.email_config['admin_password'])
            server.send_message(msg)
            server.quit()
            """

        except Exception as e:
            print(f"Error sending email: {e}")

    def run_auto_status_updates(self):
        """Run automatic status updates for eligible orders"""
        try:
            orders_to_update = self.get_orders_for_auto_transition()

            for order_id, current_status, next_status in orders_to_update:
                success = self.update_order_status(
                    order_id,
                    next_status,
                    changed_by='auto_system',
                    change_reason=f'Automatic transition from {current_status} after time threshold'
                )

                if success:
                    print(f"Auto-updated Order {order_id}: {current_status} -> {next_status}")
                else:
                    print(f"Failed to auto-update Order {order_id}")

        except Exception as e:
            print(f"Error running auto status updates: {e}")

# Global instance
order_status_manager = OrderStatusManager()
