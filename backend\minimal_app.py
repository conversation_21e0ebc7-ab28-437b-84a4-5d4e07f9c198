from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def home():
    return jsonify({'message': 'Grace Medical API is running!', 'status': 'success'})

@app.route('/api/test')
def test():
    return jsonify({'message': 'Test endpoint working!', 'status': 'success'})

@app.route('/api/products')
def get_products():
    mock_products = [
        {
            "id": 1,
            "name": "Disposable Syringes 10ml",
            "description": "Sterile single-use syringes for medical injections",
            "unit_price": 25.99,
            "stock_quantity": 500
        },
        {
            "id": 2,
            "name": "Nitrile Examination Gloves",
            "description": "Powder-free nitrile gloves for medical examination",
            "unit_price": 18.50,
            "stock_quantity": 800
        }
    ]
    return jsonify({'products': mock_products, 'total': len(mock_products)})

if __name__ == '__main__':
    print("Starting Grace Medical API...")
    print("Available endpoints:")
    print("- GET / - Home page")
    print("- GET /api/test - Test endpoint")
    print("- GET /api/products - Products list")
    app.run(debug=True, host='0.0.0.0', port=5000)
