import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { productsAPI } from '../utils/api'
import { useCart } from '../contexts/CartContext'
import { ArrowLeft, ShoppingCart, Package, Calendar, Building, AlertCircle } from 'lucide-react'
import toast from 'react-hot-toast'

const ProductDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { addToCart } = useCart()
  const [product, setProduct] = useState(null)
  const [loading, setLoading] = useState(true)
  const [quantity, setQuantity] = useState(1)
  const [addingToCart, setAddingToCart] = useState(false)

  useEffect(() => {
    fetchProduct()
  }, [id])

  const fetchProduct = async () => {
    try {
      setLoading(true)
      const response = await productsAPI.getProduct(id)
      setProduct(response.data)
    } catch (error) {
      toast.error('Product not found')
      navigate('/products')
    } finally {
      setLoading(false)
    }
  }

  const handleAddToCart = async () => {
    setAddingToCart(true)
    try {
      addToCart(product, quantity)
      toast.success(`${product.name} added to cart`)
    } catch (error) {
      toast.error('Failed to add to cart')
    } finally {
      setAddingToCart(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Product not found</h2>
          <button onClick={() => navigate('/products')} className="btn-primary">
            Back to Products
          </button>
        </div>
      </div>
    )
  }

  const isLowStock = product.stock_quantity <= product.reorder_level
  const isOutOfStock = product.stock_quantity === 0
  const currentPrice = quantity >= 10 && product.bulk_price ? product.bulk_price : product.unit_price

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <button
        onClick={() => navigate('/products')}
        className="flex items-center text-gray-600 hover:text-gray-900 mb-6"
      >
        <ArrowLeft className="h-5 w-5 mr-2" />
        Back to Products
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Image */}
        <div>
          <img
            src={product.image_url || `https://via.placeholder.com/400x400/e5e7eb/6b7280?text=${encodeURIComponent(product.name?.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '+') || 'Medical+Supply')}`}
            alt={product.name}
            className="w-full h-96 object-cover rounded-lg shadow-md"
            onError={(e) => {
              e.target.src = `https://via.placeholder.com/400x400/e5e7eb/6b7280?text=${encodeURIComponent('Medical+Supply')}`
            }}
          />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
            <p className="text-gray-600">{product.description}</p>
          </div>

          {/* Stock Status */}
          {isOutOfStock ? (
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              Out of Stock
            </div>
          ) : isLowStock ? (
            <div className="flex items-center text-yellow-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              Low Stock ({product.stock_quantity} remaining)
            </div>
          ) : (
            <div className="flex items-center text-green-600">
              <Package className="h-5 w-5 mr-2" />
              In Stock ({product.stock_quantity} available)
            </div>
          )}

          {/* Product Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-900">Packaging:</span>
              <p className="text-gray-600">{product.packaging}</p>
            </div>
            <div>
              <span className="font-medium text-gray-900">Category:</span>
              <p className="text-gray-600">{product.category}</p>
            </div>
            <div>
              <span className="font-medium text-gray-900">Manufacturer:</span>
              <p className="text-gray-600 flex items-center">
                <Building className="h-4 w-4 mr-1" />
                {product.manufacturer}
              </p>
            </div>
            {product.expiry_date && (
              <div>
                <span className="font-medium text-gray-900">Expiry Date:</span>
                <p className="text-gray-600 flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {new Date(product.expiry_date).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>

          {/* Pricing */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-primary-600 mb-2">
              ${currentPrice.toFixed(2)}
              {quantity >= 10 && product.bulk_price && (
                <span className="text-sm text-gray-500 ml-2">(Bulk Price)</span>
              )}
            </div>
            {product.bulk_price && (
              <div className="text-sm text-gray-600">
                Unit Price: ${product.unit_price} | Bulk Price: ${product.bulk_price} (10+ items)
              </div>
            )}
          </div>

          {/* Quantity and Add to Cart */}
          {!isOutOfStock && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="btn-secondary px-3 py-1"
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min="1"
                    max={product.stock_quantity}
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, Math.min(product.stock_quantity, parseInt(e.target.value) || 1)))}
                    className="input-field w-20 text-center"
                  />
                  <button
                    onClick={() => setQuantity(Math.min(product.stock_quantity, quantity + 1))}
                    className="btn-secondary px-3 py-1"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="text-lg font-semibold text-gray-900">
                Total: ${(currentPrice * quantity).toFixed(2)}
              </div>

              <button
                onClick={handleAddToCart}
                disabled={addingToCart}
                className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {addingToCart ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <>
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    Add to Cart
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProductDetail
