<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grace Medical API - CORS Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .loading { color: #6c757d; }
    </style>
</head>
<body>
    <h1>🧪 Grace Medical API - CORS Test</h1>
    <p>This page tests CORS functionality between frontend and Flask backend.</p>
    
    <div class="test-section">
        <h3>🏠 Test Root Endpoint</h3>
        <button onclick="testEndpoint('/')">Test GET /</button>
        <div id="root-result"></div>
    </div>
    
    <div class="test-section">
        <h3>🔍 Test API Root</h3>
        <button onclick="testEndpoint('/api')">Test GET /api</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h3>📦 Test Products</h3>
        <button onclick="testEndpoint('/api/products')">Test GET /api/products</button>
        <div id="products-result"></div>
    </div>
    
    <div class="test-section">
        <h3>💚 Test Health Check</h3>
        <button onclick="testEndpoint('/api/health')">Test GET /api/health</button>
        <div id="health-result"></div>
    </div>
    
    <div class="test-section">
        <h3>🔐 Test Login</h3>
        <button onclick="testLogin()">Test POST /api/auth/login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>🧪 Test All Endpoints</h3>
        <button onclick="testAllEndpoints()">Run All Tests</button>
        <div id="all-results"></div>
    </div>
    
    <div class="test-section">
        <h3>🏷️ Test Categories</h3>
        <button onclick="testEndpoint('/api/categories')">Test GET /api/categories</button>
        <div id="categories-result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:5000';
        
        async function testEndpoint(endpoint) {
            const resultId = endpoint.replace(/[^a-zA-Z]/g, '') + '-result';
            const resultDiv = document.getElementById(resultId);
            
            resultDiv.innerHTML = '<div class="loading">🔄 Testing...</div>';
            
            try {
                const response = await fetch(API_BASE + endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>✅ Success!</strong><br>
                        Status: ${response.status}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ Error!</strong><br>
                        ${error.message}<br>
                        <small>Check browser console for details</small>
                    </div>
                `;
                console.error('API Error:', error);
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<div class="loading">🔄 Testing login...</div>';
            
            try {
                const response = await fetch(API_BASE + '/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ Login Success!</strong><br>
                            User: ${data.user.email}<br>
                            Admin: ${data.user.is_admin}<br>
                            Token: ${data.access_token ? 'Received' : 'Missing'}<br>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ Login Failed!</strong><br>
                            ${data.error || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ Network Error!</strong><br>
                        ${error.message}<br>
                        <small>Check if backend is running and CORS is configured</small>
                    </div>
                `;
                console.error('Login Error:', error);
            }
        }
        
        async function testAllEndpoints() {
            const resultDiv = document.getElementById('all-results');
            resultDiv.innerHTML = '<div class="loading">🔄 Running all tests...</div>';

            const endpoints = [
                { name: 'Root', url: '/', method: 'GET' },
                { name: 'API Root', url: '/api', method: 'GET' },
                { name: 'Products', url: '/api/products', method: 'GET' },
                { name: 'Health', url: '/api/health', method: 'GET' },
                { name: 'Categories', url: '/api/categories', method: 'GET' }
            ];

            let results = [];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(API_BASE + endpoint.url, {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    });

                    results.push({
                        name: endpoint.name,
                        status: response.status,
                        success: response.ok,
                        cors: response.headers.get('Access-Control-Allow-Origin') ? 'Yes' : 'No'
                    });
                } catch (error) {
                    results.push({
                        name: endpoint.name,
                        status: 'ERROR',
                        success: false,
                        error: error.message,
                        cors: 'No'
                    });
                }
            }

            // Display results
            let html = '<h4>📊 Test Results:</h4><table border="1" style="width:100%; border-collapse: collapse;">';
            html += '<tr><th>Endpoint</th><th>Status</th><th>CORS</th><th>Result</th></tr>';

            results.forEach(result => {
                const rowClass = result.success ? 'success' : 'error';
                const statusIcon = result.success ? '✅' : '❌';
                html += `<tr class="${rowClass}">
                    <td>${result.name}</td>
                    <td>${result.status}</td>
                    <td>${result.cors}</td>
                    <td>${statusIcon} ${result.success ? 'Success' : (result.error || 'Failed')}</td>
                </tr>`;
            });

            html += '</table>';

            const allPassed = results.every(r => r.success);
            if (allPassed) {
                html += '<div class="success"><strong>🎉 All tests passed! CORS is working correctly.</strong></div>';
            } else {
                html += '<div class="error"><strong>⚠️ Some tests failed. Check CORS configuration.</strong></div>';
            }

            resultDiv.innerHTML = html;
        }

        // Test all endpoints on page load
        window.onload = function() {
            console.log('🚀 Grace Medical API CORS Test Page Loaded');
            console.log('Backend URL:', API_BASE);
            console.log('Click buttons above to test individual endpoints');

            // Auto-run all tests
            setTimeout(() => {
                testAllEndpoints();
            }, 1000);
        };
    </script>
</body>
</html>
