#!/bin/bash

echo "Starting Grace Medical Limited Development Environment..."
echo

# Check Python installation
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ from https://python.org/downloads/"
    exit 1
fi

# Check Node.js installation
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js 16+ from https://nodejs.org/downloads/"
    exit 1
fi

echo "Setting up backend..."
cd backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment and install dependencies
echo "Installing Python dependencies..."
source venv/bin/activate
pip install -r requirements.txt

# Start backend in background
echo "Starting backend server..."
nohup python app.py > ../backend.log 2>&1 &
BACKEND_PID=$!
echo "Backend started with PID: $BACKEND_PID"

cd ../frontend

# Install frontend dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing Node.js dependencies..."
    npm install
fi

# Start frontend in background
echo "Starting frontend development server..."
nohup npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo "Frontend started with PID: $FRONTEND_PID"

echo
echo "========================================"
echo "Grace Medical Limited is starting up!"
echo "========================================"
echo
echo "Backend will be available at: http://localhost:5000"
echo "Frontend will be available at: http://localhost:3000"
echo
echo "Default Admin Login:"
echo "Email: <EMAIL>"
echo "Password: admin123"
echo
echo "Logs:"
echo "Backend: tail -f backend.log"
echo "Frontend: tail -f frontend.log"
echo
echo "To stop the servers:"
echo "kill $BACKEND_PID $FRONTEND_PID"
echo

# Wait a moment for servers to start
sleep 5

# Try to open browser (works on most Linux distributions)
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:3000
elif command -v open &> /dev/null; then
    open http://localhost:3000
else
    echo "Please open http://localhost:3000 in your browser"
fi

echo "Development servers are running in the background."
echo "Press Ctrl+C to stop monitoring, or run the kill command above to stop servers."

# Keep script running and show logs
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
tail -f frontend.log
