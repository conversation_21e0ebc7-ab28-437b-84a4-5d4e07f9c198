#!/usr/bin/env python3
"""
Force database migration - manually update image URLs
"""

import sqlite3
import os

def force_migration():
    """Force update the database with proper image URLs"""
    db_path = os.path.join(os.path.dirname(__file__), 'grace_medical.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print("🔧 Force updating database with proper image URLs...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current orders table schema
        print("\n📋 Checking orders table schema...")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        # Add phone column if missing
        if 'phone' not in column_names:
            print("📞 Adding phone column...")
            cursor.execute('ALTER TABLE orders ADD COLUMN phone TEXT')
            print("✅ Phone column added")
        else:
            print("✅ Phone column already exists")
        
        # Add email column if missing
        if 'email' not in column_names:
            print("📧 Adding email column...")
            cursor.execute('ALTER TABLE orders ADD COLUMN email TEXT')
            print("✅ Email column added")
        else:
            print("✅ Email column already exists")
        
        # Update ALL products with proper placeholder image URLs
        print("\n🖼️ Updating ALL product images with placeholders...")
        
        cursor.execute('SELECT id, name, image_url FROM products')
        all_products = cursor.fetchall()
        
        updated_count = 0
        for product_id, product_name, current_url in all_products:
            # Create a placeholder image URL based on product name
            safe_name = product_name.replace(' ', '+').replace('&', 'and').replace('/', '')
            placeholder_url = f"https://via.placeholder.com/300x200/e5e7eb/6b7280?text={safe_name}"
            
            # Update the product
            cursor.execute('''
                UPDATE products 
                SET image_url = ? 
                WHERE id = ?
            ''', (placeholder_url, product_id))
            
            updated_count += 1
            if updated_count <= 5:  # Show first 5 updates
                print(f"  ✅ Updated ID {product_id}: {product_name}")
                print(f"     New URL: {placeholder_url}")
        
        print(f"\n🎉 Updated {updated_count} products with placeholder images")
        
        # Commit changes
        conn.commit()
        
        # Verify the changes
        print("\n🔍 Verifying changes...")
        cursor.execute('SELECT id, name, image_url FROM products LIMIT 3')
        sample_products = cursor.fetchall()
        
        for product_id, name, image_url in sample_products:
            print(f"  ID {product_id}: {name}")
            print(f"    Image URL: {image_url}")
            print()
        
        print("✅ Database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🔧 Grace Medical Limited - Force Database Migration")
    print("=" * 50)
    
    success = force_migration()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. The database has been updated with placeholder images")
        print("2. Refresh your browser to see the changes")
        print("3. Test order placement - phone column error should be fixed")
    else:
        print("\n❌ Migration failed - please check the error above")
