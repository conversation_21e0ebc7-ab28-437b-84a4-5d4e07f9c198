# Grace Medical Limited - Deployment Guide

## Production Deployment Options

### Option 1: Traditional Server Deployment

#### Prerequisites
- Ubuntu 20.04+ or CentOS 8+ server
- Domain name with DNS configured
- SSL certificate (Let's Encrypt recommended)
- Minimum 2GB RAM, 20GB storage

#### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install python3 python3-pip python3-venv nodejs npm nginx supervisor -y

# Install PM2 for Node.js process management
sudo npm install -g pm2
```

#### 2. Application Deployment
```bash
# Clone repository
git clone <your-repo-url> /var/www/grace-medical
cd /var/www/grace-medical

# Backend setup
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Frontend setup
cd ../frontend
npm install
npm run build
```

#### 3. Environment Configuration
```bash
# Create production environment file
sudo nano /var/www/grace-medical/backend/.env
```

```env
FLASK_ENV=production
FLASK_DEBUG=False
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here
DATABASE_URL=sqlite:///grace_medical_prod.db
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
ADMIN_EMAIL=<EMAIL>
API_KEY=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
```

#### 4. Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/grace-medical
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Frontend
    location / {
        root /var/www/grace-medical/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files
    location /images {
        root /var/www/grace-medical/frontend/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/grace-medical /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 5. Process Management with Supervisor
```bash
sudo nano /etc/supervisor/conf.d/grace-medical-backend.conf
```

```ini
[program:grace-medical-backend]
command=/var/www/grace-medical/backend/venv/bin/python app.py
directory=/var/www/grace-medical/backend
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/grace-medical-backend.log
environment=PATH="/var/www/grace-medical/backend/venv/bin"
```

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start grace-medical-backend
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile for Backend
```dockerfile
# backend/Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "app.py"]
```

#### 2. Create Dockerfile for Frontend
```dockerfile
# frontend/Dockerfile
FROM node:16-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

#### 3. Docker Compose Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///grace_medical.db
    volumes:
      - ./backend/data:/app/data
      - backend_db:/app
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  backend_db:
```

#### 4. Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Update application
git pull
docker-compose build
docker-compose up -d
```

### Option 3: Cloud Platform Deployment

#### Heroku Deployment

1. **Prepare for Heroku**
```bash
# Create Procfile for backend
echo "web: python app.py" > backend/Procfile

# Create runtime.txt
echo "python-3.9.16" > backend/runtime.txt
```

2. **Deploy Backend**
```bash
cd backend
heroku create grace-medical-api
heroku config:set FLASK_ENV=production
heroku config:set JWT_SECRET_KEY=your-secret-key
heroku config:set API_KEY=your-api-key
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

3. **Deploy Frontend (Netlify/Vercel)**
```bash
cd frontend
npm run build
# Upload dist folder to Netlify or connect GitHub to Vercel
```

#### AWS Deployment

1. **EC2 Instance Setup**
- Launch Ubuntu 20.04 EC2 instance
- Configure security groups (ports 80, 443, 22)
- Follow traditional server deployment steps

2. **RDS Database (Optional)**
```python
# Update backend/app.py for PostgreSQL
import os
import psycopg2

app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
```

3. **S3 for Static Files**
- Upload product images to S3 bucket
- Update image URLs in CSV file
- Configure CloudFront for CDN

## Database Migration (Production)

### SQLite to PostgreSQL
```python
# migration_script.py
import sqlite3
import psycopg2
from psycopg2.extras import RealDictCursor

def migrate_data():
    # Connect to SQLite
    sqlite_conn = sqlite3.connect('grace_medical.db')
    sqlite_conn.row_factory = sqlite3.Row
    
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(
        host='your-host',
        database='grace_medical',
        user='your-user',
        password='your-password'
    )
    
    # Migrate tables
    # ... migration logic
```

## SSL Certificate Setup

### Let's Encrypt (Free)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### 1. Application Monitoring
```python
# Add to backend/app.py
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/grace_medical.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

### 2. System Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs -y

# Setup log rotation
sudo nano /etc/logrotate.d/grace-medical
```

```
/var/log/grace-medical*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## Backup Strategy

### 1. Database Backup
```bash
#!/bin/bash
# backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/grace-medical"
mkdir -p $BACKUP_DIR

# SQLite backup
cp /var/www/grace-medical/backend/grace_medical.db $BACKUP_DIR/grace_medical_$DATE.db

# Compress and upload to cloud storage
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $BACKUP_DIR/grace_medical_$DATE.db
# aws s3 cp $BACKUP_DIR/backup_$DATE.tar.gz s3://your-backup-bucket/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
```

### 2. Application Backup
```bash
#!/bin/bash
# backup_app.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /var/backups/grace-medical-app_$DATE.tar.gz /var/www/grace-medical
```

## Performance Optimization

### 1. Backend Optimization
```python
# Add caching
from flask_caching import Cache

cache = Cache(app, config={'CACHE_TYPE': 'simple'})

@app.route('/api/products')
@cache.cached(timeout=300)  # Cache for 5 minutes
def get_products():
    # ... existing code
```

### 2. Frontend Optimization
```javascript
// Add service worker for caching
// frontend/public/sw.js
const CACHE_NAME = 'grace-medical-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});
```

### 3. Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_stock ON products(stock_quantity);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

## Security Checklist

- [ ] Change default admin password
- [ ] Use secure JWT secret key
- [ ] Enable HTTPS with valid SSL certificate
- [ ] Configure proper CORS settings
- [ ] Implement rate limiting
- [ ] Regular security updates
- [ ] Database access restrictions
- [ ] API key rotation policy
- [ ] Input validation and sanitization
- [ ] Error message sanitization
- [ ] File upload restrictions
- [ ] SQL injection prevention
- [ ] XSS protection

## Maintenance Tasks

### Daily
- Monitor application logs
- Check system resources
- Verify backup completion

### Weekly
- Review security logs
- Update dependencies
- Performance monitoring

### Monthly
- Security patches
- Database optimization
- Backup testing
- SSL certificate renewal check
