from flask import Flask, request, jsonify, send_from_directory
import os
import json
import csv
import sqlite3
from datetime import datetime, timedelta
import uuid
import hashlib
from functools import wraps

app = Flask(__name__)
app.config['SECRET_KEY'] = 'grace-medical-secret-key-2024'

# Database file path
DB_PATH = 'grace_medical.db'
API_KEY = '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'

# Simple CORS headers
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,x-api-key')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@app.route('/api/<path:path>', methods=['OPTIONS'])
def handle_options(path):
    return '', 200

# Simple authentication decorator
def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('x-api-key')
        if not api_key or api_key != API_KEY:
            return jsonify({'error': 'Invalid or missing API key'}), 403
        return f(*args, **kwargs)
    return decorated_function

def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def init_database():
    """Initialize the database with tables"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            phone TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            is_verified BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create products table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            packaging TEXT,
            unit_price REAL NOT NULL,
            bulk_price REAL,
            manufacturer TEXT,
            expiry_date DATE,
            stock_quantity INTEGER DEFAULT 0,
            reorder_level INTEGER DEFAULT 10,
            category TEXT,
            image_url TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create orders table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            order_number TEXT UNIQUE NOT NULL,
            status TEXT DEFAULT 'Pending',
            total_amount REAL NOT NULL,
            shipping_address TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Create order_items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')
    
    conn.commit()
    conn.close()

def import_products_from_csv():
    """Import products from CSV file"""
    csv_path = os.path.join('data', 'surgical_supply_catalog_100items.csv')
    
    if not os.path.exists(csv_path):
        print(f"CSV file not found at {csv_path}")
        return False
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Check if products already exist
    cursor.execute('SELECT COUNT(*) FROM products')
    if cursor.fetchone()[0] > 0:
        print("Products already exist in database, skipping CSV import")
        conn.close()
        return True
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            products_added = 0
            
            for row in csv_reader:
                # Parse expiry date
                expiry_date = None
                if row['expiry_date']:
                    try:
                        expiry_date = datetime.strptime(row['expiry_date'], '%Y-%m-%d').date()
                    except:
                        expiry_date = None
                
                cursor.execute('''
                    INSERT INTO products (
                        name, description, packaging, unit_price, bulk_price,
                        manufacturer, expiry_date, stock_quantity, reorder_level,
                        category, image_url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row['name'],
                    row['description'],
                    row['packaging'],
                    float(row['unit_price']),
                    float(row['bulk_price']) if row['bulk_price'] else None,
                    row['manufacturer'],
                    expiry_date,
                    int(row['stock_quantity']),
                    int(row['reorder_level']),
                    row.get('category', 'General'),
                    row.get('image_url', '/images/default-product.jpg')
                ))
                products_added += 1
        
        conn.commit()
        print(f"Successfully imported {products_added} products from CSV")
        return True
        
    except Exception as e:
        print(f"Error importing products from CSV: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

def create_admin_user():
    """Create default admin user if none exists"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM users WHERE is_admin = 1')
    if cursor.fetchone()[0] == 0:
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, is_admin, is_verified)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            '<EMAIL>',
            hash_password('admin123'),
            'Admin',
            'User',
            True,
            True
        ))
        conn.commit()
        print("Default admin user created: <EMAIL> / admin123")
    
    conn.close()

# Authentication Routes
@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if user already exists
        cursor.execute('SELECT id FROM users WHERE email = ?', (data['email'],))
        if cursor.fetchone():
            return jsonify({'error': 'Email already registered'}), 400
        
        # Create new user
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, phone)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            data['email'],
            hash_password(data['password']),
            data['first_name'],
            data['last_name'],
            data.get('phone', '')
        ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'Registration successful. You can now log in.',
            'user': {
                'email': data['email'],
                'first_name': data['first_name'],
                'last_name': data['last_name']
            }
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, email, password_hash, first_name, last_name, is_admin, is_verified
            FROM users WHERE email = ?
        ''', (data['email'],))
        
        user = cursor.fetchone()
        conn.close()
        
        if not user or user[2] != hash_password(data['password']):
            return jsonify({'error': 'Invalid email or password'}), 401
        
        # Create a simple token (in production, use proper JWT)
        token = f"user_{user[0]}_{uuid.uuid4().hex}"
        
        return jsonify({
            'access_token': token,
            'user': {
                'id': user[0],
                'email': user[1],
                'first_name': user[3],
                'last_name': user[4],
                'is_admin': bool(user[5]),
                'is_verified': bool(user[6])
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Product Routes
@app.route('/api/products', methods=['GET'])
def get_products():
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        category = request.args.get('category')
        search = request.args.get('search')
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Build query
        query = 'SELECT * FROM products WHERE is_active = 1'
        params = []
        
        if category:
            query += ' AND category = ?'
            params.append(category)
        
        if search:
            query += ' AND (name LIKE ? OR description LIKE ?)'
            params.extend([f'%{search}%', f'%{search}%'])
        
        # Get total count
        count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # Add pagination
        query += ' LIMIT ? OFFSET ?'
        params.extend([per_page, (page - 1) * per_page])
        
        cursor.execute(query, params)
        products = cursor.fetchall()
        conn.close()
        
        # Convert to dict format
        product_list = []
        for product in products:
            product_dict = {
                'id': product[0],
                'name': product[1],
                'description': product[2],
                'packaging': product[3],
                'unit_price': product[4],
                'bulk_price': product[5],
                'manufacturer': product[6],
                'expiry_date': product[7],
                'stock_quantity': product[8],
                'reorder_level': product[9],
                'category': product[10],
                'image_url': product[11],
                'is_active': bool(product[12]),
                'is_low_stock': product[8] <= product[9],
                'is_near_expiry': False  # Simplified for now
            }
            product_list.append(product_dict)
        
        return jsonify({
            'products': product_list,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products WHERE id = ? AND is_active = 1', (product_id,))
        product = cursor.fetchone()
        conn.close()
        
        if not product:
            return jsonify({'error': 'Product not found'}), 404
        
        product_dict = {
            'id': product[0],
            'name': product[1],
            'description': product[2],
            'packaging': product[3],
            'unit_price': product[4],
            'bulk_price': product[5],
            'manufacturer': product[6],
            'expiry_date': product[7],
            'stock_quantity': product[8],
            'reorder_level': product[9],
            'category': product[10],
            'image_url': product[11],
            'is_active': bool(product[12]),
            'is_low_stock': product[8] <= product[9],
            'is_near_expiry': False
        }
        
        return jsonify(product_dict), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/categories', methods=['GET'])
def get_categories():
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT DISTINCT category FROM products WHERE category IS NOT NULL')
        categories = [row[0] for row in cursor.fetchall()]
        conn.close()

        return jsonify(categories), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Root route - serve a simple web interface
@app.route('/')
def home():
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grace Medical Limited</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">GM</span>
                            </div>
                            <span class="ml-2 text-xl font-bold text-gray-800">Grace Medical</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="showLogin()" class="text-primary-600 hover:text-primary-700 font-medium">Login</button>
                        <button onclick="showProducts()" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium">View Products</button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div id="main-content">
            <!-- Hero Section -->
            <div class="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
                <div class="max-w-7xl mx-auto px-4 py-16">
                    <div class="text-center">
                        <h1 class="text-4xl md:text-6xl font-bold mb-4">Grace Medical Limited</h1>
                        <p class="text-xl md:text-2xl mb-8 opacity-90">Your trusted partner for quality medical supplies</p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button onclick="showProducts()" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                Browse Products
                            </button>
                            <button onclick="showLogin()" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                                Login
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="py-16 bg-white">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">Why Choose Grace Medical?</h2>
                        <p class="text-gray-600 max-w-2xl mx-auto">We provide high-quality medical supplies with reliable service and competitive pricing.</p>
                    </div>
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-primary-600 text-2xl">🏥</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Quality Products</h3>
                            <p class="text-gray-600">Premium medical supplies from trusted manufacturers</p>
                        </div>
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-primary-600 text-2xl">🚚</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Fast Delivery</h3>
                            <p class="text-gray-600">Quick and reliable shipping to your location</p>
                        </div>
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-primary-600 text-2xl">💰</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Competitive Prices</h3>
                            <p class="text-gray-600">Best prices with bulk discounts available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Modal -->
        <div id="login-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Login</h2>
                    <button onclick="hideLogin()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                <form id="login-form" onsubmit="handleLogin(event)">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                        <input type="email" id="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" id="password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-lg">
                        Login
                    </button>
                </form>
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">Demo Admin: <EMAIL> / admin123</p>
                </div>
            </div>
        </div>

        <!-- Products Section -->
        <div id="products-section" class="hidden">
            <div class="max-w-7xl mx-auto px-4 py-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">Medical Products</h2>
                    <button onclick="showHome()" class="text-primary-600 hover:text-primary-700 font-medium">← Back to Home</button>
                </div>
                <div id="products-grid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Products will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentUser = null;
        let products = [];

        // Navigation functions
        function showHome() {
            document.getElementById('main-content').classList.remove('hidden');
            document.getElementById('products-section').classList.add('hidden');
        }

        function showProducts() {
            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('products-section').classList.remove('hidden');
            loadProducts();
        }

        function showLogin() {
            document.getElementById('login-modal').classList.remove('hidden');
            document.getElementById('login-modal').classList.add('flex');
        }

        function hideLogin() {
            document.getElementById('login-modal').classList.add('hidden');
            document.getElementById('login-modal').classList.remove('flex');
        }

        // Login function
        async function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    currentUser = data.user;
                    alert(`Welcome, ${data.user.first_name}!`);
                    hideLogin();
                    updateNavigation();
                } else {
                    alert(data.error || 'Login failed');
                }
            } catch (error) {
                alert('Login error: ' + error.message);
            }
        }

        // Load products
        async function loadProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();

                if (response.ok) {
                    products = data.products;
                    displayProducts(products);
                } else {
                    alert('Failed to load products: ' + data.error);
                }
            } catch (error) {
                alert('Error loading products: ' + error.message);
            }
        }

        // Display products
        function displayProducts(products) {
            const grid = document.getElementById('products-grid');
            grid.innerHTML = '';

            products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow';
                productCard.innerHTML = `
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">${product.name}</h3>
                        <p class="text-gray-600 text-sm mb-2">${product.description || 'No description available'}</p>
                        <p class="text-sm text-gray-500">Category: ${product.category || 'General'}</p>
                        <p class="text-sm text-gray-500">Manufacturer: ${product.manufacturer || 'N/A'}</p>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-2xl font-bold text-primary-600">$${product.unit_price}</span>
                            ${product.bulk_price ? `<span class="text-sm text-gray-500 block">Bulk: $${product.bulk_price}</span>` : ''}
                        </div>
                        <div class="text-right">
                            <p class="text-sm ${product.is_low_stock ? 'text-red-600' : 'text-green-600'}">
                                Stock: ${product.stock_quantity}
                            </p>
                            <button class="mt-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded text-sm">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                `;
                grid.appendChild(productCard);
            });
        }

        // Update navigation based on login status
        function updateNavigation() {
            // This would update the navigation to show user info, logout, etc.
            // For now, just a simple implementation
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Grace Medical Limited - Web Application Loaded');
        });
    </script>
</body>
</html>
    '''

if __name__ == '__main__':
    # Initialize database and import data
    init_database()
    import_products_from_csv()
    create_admin_user()
    
    print("Grace Medical Limited Backend Server")
    print("====================================")
    print("Server starting at: http://localhost:5000")
    print("Admin login: <EMAIL> / admin123")
    print("API Key:", API_KEY)
    print("====================================")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
