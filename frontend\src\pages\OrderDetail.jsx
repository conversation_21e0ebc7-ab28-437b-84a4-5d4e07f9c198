import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { ordersAPI } from '../utils/api'
import { ArrowLeft, Package, Calendar, MapPin, FileText, DollarSign } from 'lucide-react'
import toast from 'react-hot-toast'

const OrderDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchOrder()
  }, [id])

  const fetchOrder = async () => {
    try {
      setLoading(true)
      const response = await ordersAPI.getOrder(id)
      setOrder(response.data)
    } catch (error) {
      toast.error('Order not found')
      navigate('/orders')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'Confirmed':
        return 'bg-blue-100 text-blue-800'
      case 'Dispatched':
        return 'bg-purple-100 text-purple-800'
      case 'Delivered':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Order not found</h2>
          <button onClick={() => navigate('/orders')} className="btn-primary">
            Back to Orders
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <button
        onClick={() => navigate('/orders')}
        className="flex items-center text-gray-600 hover:text-gray-900 mb-6"
      >
        <ArrowLeft className="h-5 w-5 mr-2" />
        Back to Orders
      </button>

      {/* Order Header */}
      <div className="card mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Order #{order.order_number}
            </h1>
            <div className="flex items-center text-gray-500 mt-2">
              <Calendar className="h-4 w-4 mr-1" />
              Placed on {new Date(order.created_at).toLocaleDateString()}
            </div>
          </div>
          <span className={`px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
            {order.status}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Order Total
            </h3>
            <p className="text-2xl font-bold text-primary-600">
              ${order.total_amount?.toFixed(2)}
            </p>
          </div>
          
          {order.shipping_address && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Shipping Address
              </h3>
              <p className="text-gray-600 whitespace-pre-line">
                {order.shipping_address}
              </p>
            </div>
          )}
          
          {order.notes && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Order Notes
              </h3>
              <p className="text-gray-600">
                {order.notes}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Order Items */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
          <Package className="h-5 w-5 mr-2" />
          Order Items ({order.items?.length || 0})
        </h2>
        
        <div className="space-y-4">
          {order.items?.map((item) => (
            <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
              <img
                src={item.product?.image_url || '/images/default-product.jpg'}
                alt={item.product?.name}
                className="w-16 h-16 object-cover rounded-lg"
                onError={(e) => {
                  e.target.src = '/images/default-product.jpg'
                }}
              />
              
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">
                  {item.product?.name}
                </h3>
                <p className="text-sm text-gray-600">
                  {item.product?.description}
                </p>
                <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                  <span>Packaging: {item.product?.packaging}</span>
                  <span>Manufacturer: {item.product?.manufacturer}</span>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-semibold text-gray-900">
                  ${item.total_price?.toFixed(2)}
                </div>
                <div className="text-sm text-gray-500">
                  {item.quantity} × ${item.unit_price?.toFixed(2)}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Order Summary */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>${order.total_amount?.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Shipping</span>
                <span>Free</span>
              </div>
              <div className="flex justify-between font-semibold text-lg border-t pt-2">
                <span>Total</span>
                <span>${order.total_amount?.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderDetail
