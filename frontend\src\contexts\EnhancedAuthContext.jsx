import React, { createContext, useContext, useState, useEffect } from 'react'
import { api } from '../utils/api'
import toast from 'react-hot-toast'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    // Check if user is logged in on app start
    const token = localStorage.getItem('access_token')
    const userData = localStorage.getItem('user_data')
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData)
        setUser(parsedUser)
        setIsAuthenticated(true)
        
        // Set authorization header for API requests
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        console.log('✅ User restored from localStorage:', parsedUser)
      } catch (error) {
        console.error('❌ Error parsing user data:', error)
        localStorage.removeItem('access_token')
        localStorage.removeItem('user_data')
        setUser(null)
        setIsAuthenticated(false)
      }
    }
    
    setLoading(false)
  }, [])

  const login = async (email, password) => {
    try {
      setLoading(true)
      
      const response = await api.post('/auth/login', { email, password })
      const { access_token, user: userData } = response.data
      
      // Store authentication data
      localStorage.setItem('access_token', access_token)
      localStorage.setItem('user_data', JSON.stringify(userData))
      
      // Set authorization header
      api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`
      
      // Update state
      setUser(userData)
      setIsAuthenticated(true)
      
      console.log('✅ User logged in successfully:', userData)
      toast.success(`Welcome back, ${userData.first_name}!`, {
        duration: 3000,
        icon: '👋',
      })
      
      return { success: true, user: userData }
    } catch (error) {
      console.error('❌ Login error:', error)
      const errorMessage = error.response?.data?.error || 'Login failed'
      toast.error(errorMessage, {
        duration: 4000,
      })
      
      return { 
        success: false, 
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData) => {
    try {
      setLoading(true)
      
      const response = await api.post('/auth/register', userData)
      
      console.log('✅ User registered successfully:', response.data)
      toast.success('Registration successful! You can now log in.', {
        duration: 4000,
        icon: '🎉',
      })
      
      return { success: true, data: response.data }
    } catch (error) {
      console.error('❌ Registration error:', error)
      const errorMessage = error.response?.data?.error || 'Registration failed'
      toast.error(errorMessage, {
        duration: 4000,
      })
      
      return { 
        success: false, 
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Clear local storage
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
      
      // Clear authorization header
      delete api.defaults.headers.common['Authorization']
      
      // Update state
      setUser(null)
      setIsAuthenticated(false)
      
      console.log('✅ User logged out successfully')
      toast.success('Logged out successfully', {
        duration: 2000,
        icon: '👋',
      })
      
      return { success: true }
    } catch (error) {
      console.error('❌ Logout error:', error)
      return { success: false, error: 'Logout failed' }
    }
  }

  const updateUser = (updatedUserData) => {
    const newUserData = { ...user, ...updatedUserData }
    setUser(newUserData)
    localStorage.setItem('user_data', JSON.stringify(newUserData))
    console.log('✅ User data updated:', newUserData)
  }

  const checkAuthStatus = () => {
    const token = localStorage.getItem('access_token')
    const userData = localStorage.getItem('user_data')
    
    if (!token || !userData) {
      setUser(null)
      setIsAuthenticated(false)
      return false
    }
    
    return true
  }

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
