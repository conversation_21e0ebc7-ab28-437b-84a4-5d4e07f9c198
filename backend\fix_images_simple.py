#!/usr/bin/env python3
"""
Simple script to update product images with medical-relevant placeholders
"""
import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def get_medical_image_url(product_name):
    """Generate medical-relevant placeholder image URL based on product type"""
    
    name_lower = product_name.lower()
    
    # Simple keyword matching for medical images
    if 'syringe' in name_lower or 'injection' in name_lower:
        return 'https://via.placeholder.com/300x200/e8f4fd/1e40af?text=Medical+Syringe'
    elif 'glove' in name_lower:
        return 'https://via.placeholder.com/300x200/f0fdf4/16a34a?text=Medical+Gloves'
    elif 'mask' in name_lower:
        return 'https://via.placeholder.com/300x200/fef3c7/d97706?text=Medical+Mask'
    elif 'thermometer' in name_lower:
        return 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=Thermometer'
    elif 'stethoscope' in name_lower:
        return 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=Stethoscope'
    elif 'scissors' in name_lower:
        return 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Scissors'
    elif 'bandage' in name_lower or 'gauze' in name_lower:
        return 'https://via.placeholder.com/300x200/f0fdf4/16a34a?text=Medical+Bandage'
    elif 'tape' in name_lower:
        return 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=Medical+Tape'
    elif 'antiseptic' in name_lower or 'alcohol' in name_lower or 'solution' in name_lower:
        return 'https://via.placeholder.com/300x200/ecfdf5/059669?text=Antiseptic+Solution'
    elif 'swab' in name_lower or 'wipe' in name_lower or 'pad' in name_lower:
        return 'https://via.placeholder.com/300x200/f9fafb/6b7280?text=Medical+Swab'
    elif 'oximeter' in name_lower or 'monitor' in name_lower:
        return 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=Medical+Monitor'
    elif 'pressure' in name_lower or 'cuff' in name_lower:
        return 'https://via.placeholder.com/300x200/fef2f2/dc2626?text=Blood+Pressure'
    elif 'gown' in name_lower:
        return 'https://via.placeholder.com/300x200/f3e8ff/7c3aed?text=Medical+Gown'
    elif 'forceps' in name_lower or 'clamp' in name_lower:
        return 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Instrument'
    elif 'otoscope' in name_lower or 'scope' in name_lower:
        return 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=Medical+Scope'
    elif 'needle' in name_lower or 'suture' in name_lower:
        return 'https://via.placeholder.com/300x200/f8fafc/374151?text=Surgical+Needle'
    elif 'dressing' in name_lower:
        return 'https://via.placeholder.com/300x200/f0fdf4/16a34a?text=Wound+Dressing'
    else:
        return 'https://via.placeholder.com/300x200/f0f9ff/2563eb?text=Medical+Supply'

def main():
    """Update all product images"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get all products
        cursor.execute('SELECT id, name, image_url FROM products')
        products = cursor.fetchall()
        
        print(f"Updating images for {len(products)} medical products...")
        
        for product_id, name, current_url in products:
            # Create medical-relevant image URL
            new_url = get_medical_image_url(name)
            
            # Update the product
            cursor.execute('''
                UPDATE products 
                SET image_url = ? 
                WHERE id = ?
            ''', (new_url, product_id))
            
            print(f"Updated product {product_id}: {name}")
        
        conn.commit()
        conn.close()
        
        print("All product images updated successfully!")
        
    except Exception as e:
        print(f"Error updating images: {str(e)}")

if __name__ == "__main__":
    main()
