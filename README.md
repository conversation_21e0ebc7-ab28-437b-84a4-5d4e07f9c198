# Grace Medical Limited - Surgical Supply Ordering System

A comprehensive full-stack web application for managing surgical supply orders with separate interfaces for doctors and administrators. Built with React, Tailwind CSS, Python Flask, and SQLite.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
# Double-click start_dev.bat or run in Command Prompt
start_dev.bat
```

**Linux/macOS:**
```bash
# Make executable and run
chmod +x start_dev.sh
./start_dev.sh
```

### Option 2: Manual Setup

See [SETUP_INSTRUCTIONS.md](SETUP_INSTRUCTIONS.md) for detailed manual setup instructions.

## 📋 Features

### 👨‍⚕️ For Doctors
- ✅ Self-registration and login with email verification
- ✅ Browse 100+ surgical supplies with search and filters
- ✅ Add items to cart with bulk pricing support
- ✅ Place orders with shipping information
- ✅ Track order status (Pending → Confirmed → Dispatched → Delivered)
- ✅ View complete order history
- ✅ Mobile-responsive interface
- ✅ Real-time notifications

### 👩‍💼 For Administrators
- ✅ Comprehensive admin dashboard with statistics
- ✅ Manage product inventory and pricing
- ✅ View and update order status
- ✅ Monitor low stock items (automatic alerts)
- ✅ Track near-expiry products
- ✅ Generate detailed reports
- ✅ Email notifications for new orders
- ✅ API key protected admin routes

## 🛠️ Tech Stack

### Frontend
- **React 18** with Vite for fast development
- **Tailwind CSS** for modern, responsive styling
- **React Router** for client-side navigation
- **Axios** for API communication
- **React Hot Toast** for notifications
- **Lucide React** for icons
- **JWT** authentication with secure token storage

### Backend
- **Python Flask** RESTful API
- **SQLite** database with SQLAlchemy ORM
- **JWT** authentication with Flask-JWT-Extended
- **Flask-CORS** for cross-origin requests
- **Pandas** for CSV data processing
- **APScheduler** for background tasks
- **ReportLab** for PDF generation
- **Email** notifications (SMTP)

## 📁 Project Structure

```
grace-medical-limited/
├── 📁 backend/                    # Flask API server
│   ├── 🐍 app.py                 # Main Flask application
│   ├── 🗃️ models.py              # Database models (User, Product, Order, etc.)
│   ├── 🔐 auth.py                # Authentication utilities & decorators
│   ├── 📋 requirements.txt       # Python dependencies
│   ├── ⚙️ .env                   # Environment variables
│   └── 📁 data/                  # Database and CSV files
│       └── 📊 surgical_supply_catalog_100items.csv
├── 📁 frontend/                   # React application
│   ├── 📁 src/
│   │   ├── 📁 components/        # Reusable React components
│   │   ├── 📁 pages/             # Page components
│   │   ├── 📁 contexts/          # React contexts (Auth, Cart)
│   │   ├── 📁 utils/             # Utility functions & API calls
│   │   └── ⚛️ App.jsx            # Main App component
│   ├── 📦 package.json
│   ├── 🎨 tailwind.config.js
│   └── ⚡ vite.config.js
├── 📖 README.md                   # This file
├── 📋 SETUP_INSTRUCTIONS.md       # Detailed setup guide
├── 📚 API_DOCUMENTATION.md        # Complete API documentation
├── 🚀 DEPLOYMENT_GUIDE.md         # Production deployment guide
├── 🖥️ start_dev.bat              # Windows development startup script
└── 🐧 start_dev.sh               # Linux/macOS development startup script
```

## 🗄️ Database Schema

### Users
- Authentication and profile information
- Admin role management
- Email verification system

### Products (100 Items Included)
- Comprehensive surgical supply catalog
- Stock management with reorder levels
- Bulk pricing support
- Expiry date tracking
- Category organization

### Orders & Order Items
- Complete order lifecycle management
- Automatic order number generation
- Status tracking and updates
- Item-level pricing and quantities

### Notifications
- Real-time user notifications
- Admin alerts for low stock and expiry

## 🔑 Default Admin Account

```
Email: <EMAIL>
Password: admin123
```

**⚠️ Important:** Change this password immediately in production!

## 🌐 API Endpoints

### 🔐 Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/verify-email` - Email verification

### 📦 Products
- `GET /api/products` - Get products (with pagination, search, filters)
- `GET /api/products/{id}` - Get single product
- `GET /api/categories` - Get product categories

### 🛒 Orders
- `GET /api/orders` - Get user orders
- `POST /api/orders` - Place new order
- `GET /api/orders/{id}` - Get order details

### 👩‍💼 Admin (API Key Required)
- `GET /api/admin/orders` - Get all orders
- `PUT /api/admin/orders/{id}/status` - Update order status
- `GET /api/admin/low-stock` - Get low stock products
- `GET /api/admin/near-expiry` - Get near expiry products
- `GET /api/admin/reports` - Generate reports

### 🔔 Notifications
- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/{id}/read` - Mark as read

## 📊 Sample Data

The system includes a comprehensive CSV file with 100 real surgical supply items:

- **Syringes**: Various sizes (1ml, 5ml, 10ml) from different manufacturers
- **Gloves**: Nitrile, latex, vinyl in all sizes
- **PPE**: Masks, gowns, face shields, caps
- **Instruments**: Thermometers, stethoscopes, scissors, forceps
- **Antiseptics**: Solutions, wipes, scrubs
- **Bandages**: Gauze, elastic, adhesive, specialized dressings
- **Tape**: Medical, surgical, waterproof varieties

Each item includes:
- Detailed descriptions and packaging information
- Unit and bulk pricing
- Stock quantities and reorder levels
- Expiry dates and manufacturer details
- Category classification

## 🔒 Security Features

- **JWT Authentication** with secure token storage
- **API Key Protection** for admin routes
- **Password Hashing** with Werkzeug
- **Input Validation** and sanitization
- **CORS Configuration** for secure cross-origin requests
- **Environment Variables** for sensitive configuration

## 📱 Mobile Responsive

The application is fully responsive and works seamlessly on:
- 📱 Mobile phones (iOS/Android)
- 📱 Tablets
- 💻 Desktop computers
- 🖥️ Large screens

## 🚀 Deployment Options

- **Traditional Server** (Ubuntu/CentOS with Nginx)
- **Docker** containers with Docker Compose
- **Cloud Platforms** (Heroku, AWS, DigitalOcean)
- **Static Hosting** (Netlify/Vercel for frontend)

See [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md) for detailed deployment instructions.

## 📚 Documentation

- 📋 [Setup Instructions](SETUP_INSTRUCTIONS.md) - Detailed installation guide
- 📚 [API Documentation](API_DOCUMENTATION.md) - Complete API reference
- 🚀 [Deployment Guide](DEPLOYMENT_GUIDE.md) - Production deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

If you encounter any issues:

1. Check the [Setup Instructions](SETUP_INSTRUCTIONS.md)
2. Review the [API Documentation](API_DOCUMENTATION.md)
3. Ensure all prerequisites are installed
4. Check console logs for error messages
5. Verify all services are running on correct ports

## 🎯 Key Features Implemented

- ✅ **Complete Authentication System** with JWT and email verification
- ✅ **Product Catalog** with 100 real surgical items
- ✅ **Shopping Cart** with bulk pricing and persistent storage
- ✅ **Order Management** with status tracking
- ✅ **Admin Dashboard** with comprehensive statistics
- ✅ **Stock Management** with automatic low stock alerts
- ✅ **Expiry Tracking** with near-expiry notifications
- ✅ **Mobile Responsive** design with Tailwind CSS
- ✅ **API Key Protection** for admin routes
- ✅ **Automatic CSV Import** on first startup
- ✅ **Real-time Notifications** system
- ✅ **Background Tasks** for monitoring and alerts

---

**Grace Medical Limited** - Your trusted partner for quality surgical supplies and medical equipment. 🏥💙
