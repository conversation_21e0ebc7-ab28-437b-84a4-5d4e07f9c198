# Grace Medical API Documentation

## 🚀 Backend Status: FULLY FUNCTIONAL ✅

The Flask backend is running successfully at `http://localhost:5000` with all endpoints working correctly for frontend integration.

## 📋 API Endpoints Overview

### Root Endpoints
- **`GET /`** - Welcome message with API overview
- **`GET /api`** - Detailed API documentation and endpoints
- **`GET /api/health`** - Health check with system status

### Authentication
- **`POST /api/auth/login`** - User login (returns JWT token)
- **`POST /api/auth/register`** - User registration
- **`POST /api/auth/verify-email`** - Email verification (mock)

### Products
- **`GET /api/products`** - Get products with filtering and pagination
- **`GET /api/products/<id>`** - Get single product
- **`POST /api/products`** - Create product (requires API key)
- **`PUT /api/products/<id>`** - Update product (requires API key)

### Orders
- **`GET /api/orders`** - Get user orders (requires JWT)
- **`POST /api/orders`** - Create order (requires JWT)
- **`GET /api/orders/<id>`** - Get single order (requires JWT)

### Admin Routes (require API key)
- **`GET /api/admin/orders`** - Get all orders
- **`PUT /api/admin/orders/<id>/status`** - Update order status
- **`GET /api/admin/products/low-stock`** - Get low stock products
- **`GET /api/admin/products/near-expiry`** - Get near expiry products
- **`GET /api/admin/reports`** - Get analytics reports

### Utility
- **`GET /api/categories`** - Get product categories
- **`GET /api/notifications`** - Get notifications (mock)

## 🔐 Authentication Details

### Default Admin User
- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** Admin

### API Key for Admin Routes
```
X-API-Key: 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
```

### JWT Token Usage
After login, include the token in requests:
```
Authorization: Bearer <your-jwt-token>
```

## 📦 Mock Data Available

### Products (5 items)
1. Disposable Syringes 10ml - $25.99
2. Nitrile Examination Gloves - $18.50
3. Surgical Face Masks - $12.99
4. Digital Thermometer - $45.00
5. Antiseptic Solution 500ml - $8.75

### Categories
- Syringes
- Gloves
- PPE
- Instruments
- Antiseptics

## 🧪 Testing

Run the test suite:
```bash
cd backend
python test_endpoints.py
```

## 🔧 Technical Features

✅ **CORS Enabled** - Ready for frontend integration
✅ **JWT Authentication** - Secure token-based auth
✅ **API Key Protection** - Admin routes secured
✅ **Mock Data** - No database required
✅ **Error Handling** - Proper HTTP status codes
✅ **Pagination** - Products and orders support pagination
✅ **Filtering** - Products can be filtered by category, search, etc.

## 🚀 Frontend Integration Ready

The backend is fully prepared for frontend integration with:
- Clean JSON responses
- Proper CORS headers
- Consistent error handling
- Mock data for testing
- All endpoints functional

Start the server:
```bash
cd backend
python app.py
```

Server runs on: `http://localhost:5000`
