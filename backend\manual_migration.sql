-- Manual database migration script for Grace Medical Limited
-- Run this if the Python migration doesn't work

-- Add phone column to orders table if it doesn't exist
ALTER TABLE orders ADD COLUMN phone TEXT;

-- Add email column to orders table if it doesn't exist  
ALTER TABLE orders ADD COLUMN email TEXT;

-- Update products with missing image URLs
UPDATE products 
SET image_url = 'https://via.placeholder.com/300x200/e5e7eb/6b7280?text=' || REPLACE(REPLACE(name, ' ', '+'), '&', 'and')
WHERE image_url IS NULL OR image_url = '' OR image_url = 'null';

-- Verify the changes
SELECT 'Orders table columns:' as info;
PRAGMA table_info(orders);

SELECT 'Products without images:' as info;
SELECT COUNT(*) as count FROM products WHERE image_url IS NULL OR image_url = '' OR image_url = 'null';

SELECT 'Sample products with images:' as info;
SELECT id, name, image_url FROM products LIMIT 3;
