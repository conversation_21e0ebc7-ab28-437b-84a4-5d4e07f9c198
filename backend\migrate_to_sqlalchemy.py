"""
Migration script to convert existing SQLite database to SQLAlchemy format
Grace Medical Limited - Medical Supply E-commerce Application
"""

import sqlite3
import os
from datetime import datetime
from app_sqlalchemy import app, db
from database import User, Product, Order, OrderItem, Category

def migrate_existing_data():
    """Migrate data from existing SQLite database to SQLAlchemy format"""
    
    # Path to existing database
    old_db_path = os.path.join(os.path.dirname(__file__), 'grace_medical.db')
    
    if not os.path.exists(old_db_path):
        print("❌ No existing database found. Starting fresh.")
        return
    
    print("🔄 Migrating existing database to SQLAlchemy format...")
    
    with app.app_context():
        # Create new tables
        db.create_all()
        
        # Connect to old database
        old_conn = sqlite3.connect(old_db_path)
        old_conn.row_factory = sqlite3.Row
        old_cursor = old_conn.cursor()
        
        try:
            # Migrate Users
            print("👤 Migrating users...")
            old_cursor.execute("SELECT * FROM users")
            users = old_cursor.fetchall()
            
            for user_row in users:
                existing_user = User.query.filter_by(email=user_row['email']).first()
                if not existing_user:
                    user = User(
                        email=user_row['email'],
                        password_hash=user_row['password_hash'],
                        first_name=user_row['first_name'],
                        last_name=user_row['last_name'],
                        phone=user_row.get('phone', ''),
                        is_admin=bool(user_row.get('is_admin', False)),
                        is_verified=bool(user_row.get('is_verified', True)),
                        is_active=bool(user_row.get('is_active', True))
                    )
                    db.session.add(user)
            
            # Migrate Products
            print("📦 Migrating products...")
            old_cursor.execute("SELECT * FROM products")
            products = old_cursor.fetchall()
            
            for product_row in products:
                existing_product = Product.query.filter_by(name=product_row['name']).first()
                if not existing_product:
                    # Handle expiry date
                    expiry_date = None
                    if product_row.get('expiry_date'):
                        try:
                            expiry_date = datetime.strptime(product_row['expiry_date'], '%Y-%m-%d').date()
                        except (ValueError, TypeError):
                            expiry_date = None
                    
                    product = Product(
                        name=product_row['name'],
                        description=product_row.get('description', ''),
                        category=product_row.get('category', 'General'),
                        manufacturer=product_row.get('manufacturer', ''),
                        packaging=product_row.get('packaging', ''),
                        unit_price=float(product_row.get('unit_price', 0)),
                        bulk_price=float(product_row['bulk_price']) if product_row.get('bulk_price') else None,
                        stock_quantity=int(product_row.get('stock_quantity', 0)),
                        reorder_level=int(product_row.get('reorder_level', 10)),
                        image_url=product_row.get('image_url'),
                        expiry_date=expiry_date,
                        is_active=bool(product_row.get('is_active', True))
                    )
                    db.session.add(product)
            
            # Migrate Orders
            print("📋 Migrating orders...")
            old_cursor.execute("SELECT * FROM orders")
            orders = old_cursor.fetchall()
            
            for order_row in orders:
                existing_order = Order.query.filter_by(order_number=order_row['order_number']).first()
                if not existing_order:
                    order = Order(
                        order_number=order_row['order_number'],
                        user_id=order_row.get('user_id', 1),
                        status=order_row.get('status', 'Pending'),
                        total_amount=float(order_row.get('total_amount', 0)),
                        shipping_address=order_row.get('shipping_address', ''),
                        billing_address=order_row.get('billing_address'),
                        phone=order_row.get('phone', ''),
                        notes=order_row.get('notes', '')
                    )
                    
                    # Set timestamps if available
                    if order_row.get('created_at'):
                        try:
                            order.created_at = datetime.fromisoformat(order_row['created_at'].replace('Z', '+00:00'))
                        except:
                            pass
                    
                    db.session.add(order)
                    db.session.flush()  # Get the order ID
                    
                    # Migrate Order Items
                    old_cursor.execute("SELECT * FROM order_items WHERE order_id = ?", (order_row['id'],))
                    order_items = old_cursor.fetchall()
                    
                    for item_row in order_items:
                        order_item = OrderItem(
                            order_id=order.id,
                            product_id=item_row['product_id'],
                            quantity=int(item_row['quantity']),
                            unit_price=float(item_row['unit_price']),
                            total_price=float(item_row['total_price'])
                        )
                        db.session.add(order_item)
            
            # Commit all changes
            db.session.commit()
            print("✅ Migration completed successfully!")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Migration failed: {str(e)}")
            raise
        
        finally:
            old_conn.close()

def backup_existing_database():
    """Create a backup of the existing database"""
    old_db_path = os.path.join(os.path.dirname(__file__), 'grace_medical.db')
    
    if os.path.exists(old_db_path):
        backup_path = f"{old_db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(old_db_path, backup_path)
        print(f"📋 Database backed up to: {backup_path}")

if __name__ == '__main__':
    print("🔄 Grace Medical Limited - Database Migration to SQLAlchemy")
    print("=" * 60)
    
    # Create backup
    backup_existing_database()
    
    # Run migration
    migrate_existing_data()
    
    print("=" * 60)
    print("✅ Migration process completed!")
    print("🚀 You can now run the SQLAlchemy version with: python app_sqlalchemy.py")
