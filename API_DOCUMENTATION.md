# Grace Medical Limited - API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication

### JWT Token Authentication
Most endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

### API Key Authentication (Admin Routes)
Admin routes require an API key in the x-api-key header:
```
x-api-key: 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
```

## Authentication Endpoints

### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "<PERSON>",
  "last_name": "Doe",
  "phone": "+**********"
}
```

**Response:**
```json
{
  "message": "Registration successful. Please check your email for verification.",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "Doe",
    "phone": "+**********",
    "is_admin": false,
    "is_verified": false,
    "created_at": "2024-01-01T00:00:00"
  }
}
```

### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "is_admin": false,
    "is_verified": true
  }
}
```

### Verify Email
```http
POST /api/auth/verify-email
Content-Type: application/json

{
  "token": "verification-token-uuid"
}
```

## Product Endpoints

### Get Products
```http
GET /api/products?page=1&per_page=20&category=Syringes&search=disposable
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20)
- `category` (optional): Filter by category
- `search` (optional): Search in name and description

**Response:**
```json
{
  "products": [
    {
      "id": 1,
      "name": "Disposable Syringes 10ml",
      "description": "Sterile single-use syringes for medical injections",
      "packaging": "Box of 100",
      "unit_price": 25.99,
      "bulk_price": 23.99,
      "manufacturer": "MedTech Solutions",
      "expiry_date": "2025-12-31",
      "stock_quantity": 500,
      "reorder_level": 50,
      "category": "Syringes",
      "image_url": "/images/syringe-10ml.jpg",
      "is_active": true,
      "is_low_stock": false,
      "is_near_expiry": false,
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    }
  ],
  "total": 100,
  "pages": 5,
  "current_page": 1
}
```

### Get Single Product
```http
GET /api/products/1
```

**Response:**
```json
{
  "id": 1,
  "name": "Disposable Syringes 10ml",
  "description": "Sterile single-use syringes for medical injections",
  "packaging": "Box of 100",
  "unit_price": 25.99,
  "bulk_price": 23.99,
  "manufacturer": "MedTech Solutions",
  "expiry_date": "2025-12-31",
  "stock_quantity": 500,
  "reorder_level": 50,
  "category": "Syringes",
  "image_url": "/images/syringe-10ml.jpg",
  "is_active": true,
  "is_low_stock": false,
  "is_near_expiry": false,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### Get Categories
```http
GET /api/categories
```

**Response:**
```json
[
  "Syringes",
  "Gloves",
  "PPE",
  "Instruments",
  "Antiseptics",
  "Bandages",
  "Tape"
]
```

## Order Endpoints

### Get User Orders
```http
GET /api/orders?page=1&per_page=10
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "orders": [
    {
      "id": 1,
      "user_id": 1,
      "order_number": "GM20240101120000",
      "status": "Pending",
      "total_amount": 125.99,
      "shipping_address": "123 Medical Center Dr\nCity, State 12345",
      "notes": "Urgent delivery needed",
      "created_at": "2024-01-01T12:00:00",
      "updated_at": "2024-01-01T12:00:00",
      "items": [
        {
          "id": 1,
          "product_id": 1,
          "quantity": 5,
          "unit_price": 25.99,
          "total_price": 129.95,
          "product": {
            "id": 1,
            "name": "Disposable Syringes 10ml",
            "image_url": "/images/syringe-10ml.jpg"
          }
        }
      ],
      "user": {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>"
      }
    }
  ],
  "total": 5,
  "pages": 1,
  "current_page": 1
}
```

### Create Order
```http
POST /api/orders
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "items": [
    {
      "product_id": 1,
      "quantity": 5
    },
    {
      "product_id": 2,
      "quantity": 10
    }
  ],
  "shipping_address": "123 Medical Center Dr\nCity, State 12345",
  "notes": "Urgent delivery needed"
}
```

**Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "order_number": "GM20240101120000",
  "status": "Pending",
  "total_amount": 125.99,
  "shipping_address": "123 Medical Center Dr\nCity, State 12345",
  "notes": "Urgent delivery needed",
  "created_at": "2024-01-01T12:00:00",
  "updated_at": "2024-01-01T12:00:00",
  "items": [...]
}
```

### Get Single Order
```http
GET /api/orders/1
Authorization: Bearer <jwt_token>
```

## Admin Endpoints

### Get All Orders (Admin)
```http
GET /api/admin/orders?page=1&per_page=20&status=Pending
x-api-key: 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
```

### Update Order Status (Admin)
```http
PUT /api/admin/orders/1/status
x-api-key: 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
Content-Type: application/json

{
  "status": "Confirmed"
}
```

**Valid Status Values:**
- `Pending`
- `Confirmed`
- `Dispatched`
- `Delivered`

### Get Low Stock Products (Admin)
```http
GET /api/admin/low-stock
x-api-key: 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
```

### Get Near Expiry Products (Admin)
```http
GET /api/admin/near-expiry?days=30
x-api-key: 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
```

### Generate Reports (Admin)
```http
GET /api/admin/reports?start_date=2024-01-01&end_date=2024-01-31
x-api-key: 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
```

**Response:**
```json
{
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  },
  "total_orders": 25,
  "total_revenue": 2500.50,
  "status_counts": {
    "Pending": 5,
    "Confirmed": 10,
    "Dispatched": 8,
    "Delivered": 2
  },
  "top_products": [
    {
      "product_name": "Disposable Syringes 10ml",
      "quantity_sold": 150,
      "revenue": 3898.50
    }
  ],
  "low_stock_count": 12,
  "near_expiry_count": 8
}
```

## Notification Endpoints

### Get Notifications
```http
GET /api/notifications?page=1&per_page=20
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "notifications": [
    {
      "id": 1,
      "user_id": 1,
      "title": "Order Placed Successfully",
      "message": "Your order GM20240101120000 has been placed successfully. Total: $125.99",
      "type": "success",
      "is_read": false,
      "created_at": "2024-01-01T12:00:00"
    }
  ],
  "total": 10,
  "pages": 1,
  "current_page": 1,
  "unread_count": 3
}
```

### Mark Notification as Read
```http
PUT /api/notifications/1/read
Authorization: Bearer <jwt_token>
```

## Error Responses

### 400 Bad Request
```json
{
  "error": "Email and password are required"
}
```

### 401 Unauthorized
```json
{
  "error": "Invalid token"
}
```

### 403 Forbidden
```json
{
  "error": "Invalid or missing API key"
}
```

### 404 Not Found
```json
{
  "error": "Product not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "An unexpected error occurred"
}
```

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing rate limiting for:
- Authentication endpoints: 5 requests per minute
- API endpoints: 100 requests per minute
- Admin endpoints: 200 requests per minute

## Data Validation

### Required Fields
- **User Registration**: email, password, first_name, last_name
- **User Login**: email, password
- **Order Creation**: items (array with product_id and quantity)
- **Product Creation**: name, unit_price

### Field Constraints
- **Email**: Must be valid email format
- **Password**: Minimum 6 characters
- **Quantity**: Must be positive integer
- **Price**: Must be positive number
- **Order Status**: Must be one of: Pending, Confirmed, Dispatched, Delivered

## Bulk Operations

### Bulk Price Logic
- Products with `bulk_price` field offer discounted pricing
- Bulk price applies when quantity >= 10 items
- Frontend automatically calculates and displays bulk pricing

### Stock Management
- Stock is automatically decremented when orders are placed
- Low stock alerts are generated when stock <= reorder_level
- Out of stock items cannot be added to cart

## Webhooks (Future Enhancement)

Consider implementing webhooks for:
- Order status changes
- Low stock alerts
- New user registrations
- Payment confirmations
