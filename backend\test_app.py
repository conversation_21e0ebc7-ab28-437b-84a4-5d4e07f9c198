#!/usr/bin/env python3
"""
Test script to verify the Flask app runs without errors
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing Flask app import...")
    import app
    print("✓ Flask app imported successfully!")
    
    print("Testing app configuration...")
    print(f"✓ JWT Secret Key configured: {'Yes' if app.app.config.get('JWT_SECRET_KEY') else 'No'}")
    print(f"✓ API Key configured: {'Yes' if app.API_KEY else 'No'}")
    
    print("Testing mock data...")
    print(f"✓ Mock products loaded: {len(app.MOCK_PRODUCTS)} products")
    print(f"✓ Mock users loaded: {len(app.MOCK_USERS)} users")
    print(f"✓ Mock orders loaded: {len(app.MOCK_ORDERS)} orders")
    
    print("Testing route registration...")
    routes = [rule.rule for rule in app.app.url_map.iter_rules()]
    key_routes = ['/api/products', '/api/auth/login', '/api/orders']
    for route in key_routes:
        if route in routes:
            print(f"✓ Route {route} registered")
        else:
            print(f"✗ Route {route} NOT registered")
    
    print("\n🎉 All tests passed! The Flask app is ready to run.")
    print("\nTo start the server, run:")
    print("python app.py")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Make sure all required packages are installed:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"✗ Error: {e}")
    sys.exit(1)
