# Grace Medical Limited - Demo Mode

## Current Status

Due to Python dependency installation issues, the application is currently running in **Demo Mode** with mock data. This allows you to see and interact with the full frontend functionality without requiring the backend server.

## What's Working in Demo Mode

✅ **Complete Frontend Experience**
- User registration and login (simulated)
- Product catalog browsing with search and filters
- Shopping cart functionality
- Order placement and tracking
- Admin dashboard (simulated)
- Mobile-responsive design
- All UI components and interactions

✅ **Demo Accounts Available**
- **Doctor Account**: <EMAIL> / password123
- **Admin Account**: <EMAIL> / admin123

✅ **Sample Data Included**
- 8 sample surgical supply products
- Product categories and pricing
- Mock order data
- Simulated user profiles

## How to Run Demo Mode

### Option 1: Quick Start (Windows)
```bash
# Double-click or run in Command Prompt
start_frontend_only.bat
```

### Option 2: Manual Start
```bash
cd frontend
npm install
npm run dev
```

Then open http://localhost:3000 in your browser.

## What You Can Test

### As a Doctor User
1. **Registration**: Create a new account (simulated)
2. **Login**: Use <EMAIL> / password123
3. **Browse Products**: View surgical supplies with search/filter
4. **Shopping Cart**: Add items, adjust quantities, see bulk pricing
5. **Place Orders**: Complete checkout process with shipping info
6. **Order History**: View past orders and status

### As an Admin User
1. **Admin Login**: Use <EMAIL> / admin123
2. **Dashboard**: View statistics and recent activity
3. **Product Management**: See product inventory (view-only in demo)
4. **Order Management**: View all orders and status
5. **Reports**: See sales and inventory reports

## Switching to Full Backend Mode

To enable the full backend functionality:

### 1. Install Python Dependencies
```bash
cd backend
pip install Flask Flask-CORS Flask-JWT-Extended SQLAlchemy Flask-SQLAlchemy python-dotenv pandas
```

### 2. Update Frontend Configuration
Edit `frontend/src/utils/api.js` and change:
```javascript
const USE_MOCK_DATA = false; // Change from true to false
```

### 3. Start Both Servers
```bash
# Terminal 1 - Backend
cd backend
python simple_app.py

# Terminal 2 - Frontend  
cd frontend
npm run dev
```

## Features Demonstrated

### 🎨 **Modern UI/UX**
- Clean, professional medical interface
- Tailwind CSS styling
- Responsive design for all devices
- Intuitive navigation and user flows

### 🛒 **E-commerce Functionality**
- Product catalog with categories
- Search and filtering
- Shopping cart with persistence
- Bulk pricing calculations
- Order management system

### 👥 **User Management**
- Role-based access (Doctor/Admin)
- Secure authentication flow
- User profiles and preferences

### 📊 **Admin Features**
- Dashboard with key metrics
- Inventory management interface
- Order status tracking
- Reporting capabilities

### 📱 **Mobile Responsive**
- Works perfectly on phones and tablets
- Touch-friendly interface
- Optimized layouts for all screen sizes

## Technical Implementation

### Frontend Architecture
- **React 18** with modern hooks and context
- **Vite** for fast development and building
- **Tailwind CSS** for utility-first styling
- **React Router** for client-side routing
- **Context API** for state management

### Mock Data System
- Realistic surgical supply data
- Simulated API responses with delays
- Complete user authentication flow
- Order processing simulation

### Code Quality
- Clean, maintainable component structure
- Proper error handling and loading states
- Accessible UI components
- TypeScript-ready codebase

## Next Steps

1. **Install Python Dependencies**: Follow the backend setup instructions
2. **Test Full Integration**: Switch to backend mode and test complete functionality
3. **Customize Data**: Replace mock data with your actual surgical supply catalog
4. **Deploy**: Use the deployment guide for production setup

## Support

If you need help with:
- Installing Python dependencies
- Setting up the full backend
- Customizing the application
- Deployment assistance

Please refer to the comprehensive documentation:
- `SETUP_INSTRUCTIONS.md` - Detailed setup guide
- `API_DOCUMENTATION.md` - Complete API reference
- `DEPLOYMENT_GUIDE.md` - Production deployment

## Demo Limitations

⚠️ **Note**: In demo mode, the following features are simulated:
- Email verification (automatically approved)
- Payment processing (simulated)
- File uploads (placeholder images)
- Real-time notifications (mock data)
- Database persistence (session-only)

All these features work fully when the backend is properly set up.

---

**Grace Medical Limited** - Experience the complete surgical supply ordering system! 🏥💙
