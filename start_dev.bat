@echo off
echo Starting Grace Medical Limited Development Environment...
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org/downloads/
    pause
    exit /b 1
)

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 16+ from https://nodejs.org/downloads/
    pause
    exit /b 1
)

echo.
echo Setting up backend...
cd backend

echo Installing Python dependencies...
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
)

call venv\Scripts\activate
pip install -r requirements.txt

echo.
echo Starting backend server...
start "Grace Medical Backend" cmd /k "venv\Scripts\activate && python app.py"

cd ..\frontend

echo.
echo Installing frontend dependencies...
if not exist node_modules (
    echo Installing Node.js dependencies...
    npm install
)

echo.
echo Starting frontend development server...
start "Grace Medical Frontend" cmd /k "npm run dev"

echo.
echo ========================================
echo Grace Medical Limited is starting up!
echo ========================================
echo.
echo Backend will be available at: http://localhost:5000
echo Frontend will be available at: http://localhost:3000
echo.
echo Default Admin Login:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo Press any key to open the application in your browser...
pause >nul

start http://localhost:3000

echo.
echo Development servers are running in separate windows.
echo Close those windows to stop the servers.
echo.
pause
