#!/usr/bin/env python3
"""
Setup E-commerce Database Schema
Apply comprehensive e-commerce features to Grace Medical database
"""

import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def setup_ecommerce_database():
    """Apply e-commerce schema to existing database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("🛍️ Setting up E-commerce Database Schema...")
        print("=" * 60)
        
        # Read and execute the schema file
        schema_path = os.path.join(os.path.dirname(__file__), 'ecommerce_schema.sql')
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
        
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            try:
                cursor.execute(statement)
                print(f"✅ Executed statement {i+1}/{len(statements)}")
            except sqlite3.Error as e:
                if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                    print(f"⚠️  Statement {i+1}: {str(e)} (skipping)")
                else:
                    print(f"❌ Error in statement {i+1}: {str(e)}")
        
        # Update existing products with e-commerce fields (skip SKU for now)
        print("\n🔄 Updating existing products with e-commerce data...")

        cursor.execute('SELECT id, name FROM products ORDER BY id')
        products = cursor.fetchall()

        for product_id, name in products:
            # Update product with available e-commerce fields
            try:
                cursor.execute('''
                    UPDATE products
                    SET weight = ?, dimensions = ?,
                        tax_rate = ?, requires_shipping = ?, track_inventory = ?,
                        featured = ?, sort_order = ?
                    WHERE id = ?
                ''', (0.5, "4x2x1", 8.25, True, True, product_id <= 10, product_id, product_id))
            except sqlite3.Error as e:
                print(f"⚠️  Could not update product {product_id}: {str(e)}")

        print(f"✅ Updated {len(products)} products with e-commerce data")
        
        # Add sample product categories
        print("\n📂 Adding product categories...")
        
        categories = [
            ('Syringes & Needles', 'syringes-needles', 'Medical syringes and injection equipment'),
            ('Protective Equipment', 'protective-equipment', 'PPE and safety equipment'),
            ('Diagnostic Tools', 'diagnostic-tools', 'Medical diagnostic instruments'),
            ('Wound Care', 'wound-care', 'Bandages, dressings, and wound care supplies'),
            ('Antiseptics & Solutions', 'antiseptics-solutions', 'Medical cleaning and antiseptic solutions'),
            ('Surgical Instruments', 'surgical-instruments', 'Professional surgical tools and instruments')
        ]
        
        for name, slug, description in categories:
            cursor.execute('''
                INSERT OR IGNORE INTO product_categories (name, slug, description, is_active, sort_order)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, slug, description, True, len(categories)))
        
        print(f"✅ Added {len(categories)} product categories")
        
        # Add sample shipping addresses for admin user
        print("\n🏠 Adding sample customer address...")
        
        cursor.execute('''
            INSERT OR IGNORE INTO customer_addresses 
            (user_id, address_type, first_name, last_name, address_line_1, city, state, postal_code, country, is_default)
            VALUES (1, 'both', 'Admin', 'User', '123 Medical Center Dr', 'Healthcare City', 'CA', '90210', 'United States', TRUE)
        ''')
        
        # Add sample product images
        print("\n🖼️ Adding product images...")
        
        # Get first 10 products and add their current image as primary
        cursor.execute('SELECT id, image_url FROM products WHERE image_url IS NOT NULL LIMIT 10')
        products_with_images = cursor.fetchall()
        
        for product_id, image_url in products_with_images:
            cursor.execute('''
                INSERT OR IGNORE INTO product_images (product_id, image_url, alt_text, is_primary, sort_order)
                VALUES (?, ?, ?, ?, ?)
            ''', (product_id, image_url, f"Product {product_id} image", True, 1))
        
        print(f"✅ Added images for {len(products_with_images)} products")
        
        # Create sample coupons
        print("\n🎫 Adding sample coupons...")
        
        coupons = [
            ('WELCOME10', 'percentage', 10.00, 50.00, None, 100, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
            ('FREESHIP', 'free_shipping', 0.00, 75.00, None, 50, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
            ('SAVE25', 'fixed_amount', 25.00, 100.00, None, 25, '2024-01-01 00:00:00', '2024-12-31 23:59:59')
        ]
        
        for code, type_, value, min_amount, max_discount, usage_limit, starts_at, expires_at in coupons:
            cursor.execute('''
                INSERT OR IGNORE INTO coupons 
                (code, type, value, minimum_amount, maximum_discount, usage_limit, starts_at, expires_at, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (code, type_, value, min_amount, max_discount, usage_limit, starts_at, expires_at, True))
        
        print(f"✅ Added {len(coupons)} sample coupons")
        
        conn.commit()
        conn.close()
        
        print("\n" + "=" * 60)
        print("🎉 E-commerce database setup completed successfully!")
        print("\n📊 Summary:")
        print("✅ Enhanced users table with e-commerce fields")
        print("✅ Added customer addresses table")
        print("✅ Enhanced products table with e-commerce fields")
        print("✅ Added product categories, images, and variants")
        print("✅ Added shopping cart functionality")
        print("✅ Enhanced orders with payment and shipping")
        print("✅ Added payment methods and transactions")
        print("✅ Added shipping methods")
        print("✅ Added coupons and discounts")
        print("✅ Added product reviews and wishlist")
        print("✅ Added recently viewed products")
        print("\n🛍️ Your Grace Medical app is now a full e-commerce store!")
        
    except Exception as e:
        print(f"❌ Error setting up e-commerce database: {str(e)}")

def verify_ecommerce_setup():
    """Verify the e-commerce setup"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n🔍 Verifying E-commerce Setup...")
        print("-" * 40)
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        
        ecommerce_tables = [
            'customer_addresses', 'product_categories', 'product_images', 
            'product_variants', 'shopping_cart', 'order_statuses', 
            'payment_methods', 'payment_transactions', 'shipping_methods', 
            'coupons', 'product_reviews', 'wishlist', 'recently_viewed'
        ]
        
        for table in ecommerce_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} records")
            else:
                print(f"❌ {table}: Table missing")
        
        # Check enhanced columns
        cursor.execute("PRAGMA table_info(products)")
        product_columns = [row[1] for row in cursor.fetchall()]
        
        ecommerce_columns = ['sku', 'weight', 'dimensions', 'tax_rate', 'featured']
        for column in ecommerce_columns:
            if column in product_columns:
                print(f"✅ products.{column}: Column added")
            else:
                print(f"❌ products.{column}: Column missing")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying setup: {str(e)}")

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - E-commerce Setup")
    print("=" * 60)
    
    # Setup e-commerce database
    setup_ecommerce_database()
    
    # Verify setup
    verify_ecommerce_setup()
    
    print("\n" + "=" * 60)
    print("✅ E-commerce setup completed!")
    print("🚀 Ready to launch your medical e-commerce store!")
