#!/usr/bin/env python3
"""
Test script to manually run database migration
"""

import sqlite3
import os

def test_migration():
    """Test the database migration manually"""
    db_path = os.path.join(os.path.dirname(__file__), 'grace_medical.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print("🔧 Testing database migration...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current orders table schema
        print("\n📋 Current orders table schema:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        column_names = [col[1] for col in columns]
        
        # Add phone column if missing
        if 'phone' not in column_names:
            print("\n📞 Adding phone column...")
            cursor.execute('ALTER TABLE orders ADD COLUMN phone TEXT')
            print("✅ Phone column added")
        else:
            print("\n✅ Phone column already exists")
        
        # Add email column if missing
        if 'email' not in column_names:
            print("\n📧 Adding email column...")
            cursor.execute('ALTER TABLE orders ADD COLUMN email TEXT')
            print("✅ Email column added")
        else:
            print("\n✅ Email column already exists")
        
        # Check products with missing images
        print("\n🖼️ Checking product images...")
        cursor.execute('''
            SELECT id, name, image_url FROM products 
            WHERE image_url IS NULL OR image_url = '' OR image_url = 'null'
            LIMIT 5
        ''')
        
        products_to_fix = cursor.fetchall()
        
        if products_to_fix:
            print(f"🔧 Found {len(products_to_fix)} products with missing images")
            print("Sample products to fix:")
            for product_id, product_name, current_url in products_to_fix:
                print(f"  - ID {product_id}: {product_name} (current: {current_url})")
            
            # Fix them
            for product_id, product_name, _ in products_to_fix:
                safe_name = product_name.replace(' ', '+').replace('&', 'and')
                placeholder_url = f"https://via.placeholder.com/300x200/e5e7eb/6b7280?text={safe_name}"
                
                cursor.execute('''
                    UPDATE products 
                    SET image_url = ? 
                    WHERE id = ?
                ''', (placeholder_url, product_id))
            
            print("✅ Product images fixed")
        else:
            print("✅ All products already have image URLs")
        
        # Commit changes
        conn.commit()
        
        # Verify final schema
        print("\n📋 Final orders table schema:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        print("\n🎉 Migration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Migration test failed: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = test_migration()
    if success:
        print("\n✅ Database is ready!")
        print("🚀 You can now restart your backend server")
    else:
        print("\n❌ Migration failed")
