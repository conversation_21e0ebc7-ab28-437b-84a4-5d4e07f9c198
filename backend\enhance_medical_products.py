#!/usr/bin/env python3
"""
Enhance Medical Products with Professional E-commerce Data
Add realistic medical supply product information with categories, packaging, and pricing
"""

import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def enhance_medical_products():
    """Add professional medical supply product data"""
    
    # Professional medical product data
    medical_products = [
        # Syringes & Needles
        {
            'name': 'Disposable Syringes 3ml',
            'category': 'Syringes & Needles',
            'description': 'Sterile disposable syringes with Luer lock tip for secure needle attachment',
            'packaging': 'Box of 100',
            'price': 24.99,
            'bulk_price': 22.49,
            'stock_quantity': 500,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'SYR-3ML-100'
        },
        {
            'name': 'Disposable Syringes 5ml',
            'category': 'Syringes & Needles',
            'description': 'High-quality sterile syringes with clear barrel markings for accurate dosing',
            'packaging': 'Box of 100',
            'price': 28.99,
            'bulk_price': 26.09,
            'stock_quantity': 350,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'SYR-5ML-100'
        },
        {
            'name': 'Disposable Syringes 10ml',
            'category': 'Syringes & Needles',
            'description': 'Large capacity syringes ideal for irrigation and medication administration',
            'packaging': 'Box of 50',
            'price': 19.99,
            'bulk_price': 17.99,
            'stock_quantity': 200,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'SYR-10ML-50'
        },
        {
            'name': 'Safety Needles 21G x 1"',
            'category': 'Syringes & Needles',
            'description': 'Safety needles with retractable design to prevent needlestick injuries',
            'packaging': 'Box of 100',
            'price': 45.99,
            'bulk_price': 41.39,
            'stock_quantity': 300,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'NDL-21G-100'
        },
        
        # Protective Equipment
        {
            'name': 'Nitrile Examination Gloves - Small',
            'category': 'Protective Equipment',
            'description': 'Powder-free nitrile gloves offering superior chemical resistance and tactile sensitivity',
            'packaging': 'Box of 100',
            'price': 12.99,
            'bulk_price': 11.69,
            'stock_quantity': 1000,
            'image_url': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop',
            'sku': 'GLV-NIT-S-100'
        },
        {
            'name': 'Nitrile Examination Gloves - Medium',
            'category': 'Protective Equipment',
            'description': 'Powder-free nitrile gloves offering superior chemical resistance and tactile sensitivity',
            'packaging': 'Box of 100',
            'price': 12.99,
            'bulk_price': 11.69,
            'stock_quantity': 1200,
            'image_url': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop',
            'sku': 'GLV-NIT-M-100'
        },
        {
            'name': 'Nitrile Examination Gloves - Large',
            'category': 'Protective Equipment',
            'description': 'Powder-free nitrile gloves offering superior chemical resistance and tactile sensitivity',
            'packaging': 'Box of 100',
            'price': 12.99,
            'bulk_price': 11.69,
            'stock_quantity': 800,
            'image_url': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop',
            'sku': 'GLV-NIT-L-100'
        },
        {
            'name': 'Surgical Face Masks - Level 1',
            'category': 'Protective Equipment',
            'description': 'ASTM Level 1 surgical masks with ear loops for basic protection',
            'packaging': 'Box of 50',
            'price': 8.99,
            'bulk_price': 8.09,
            'stock_quantity': 2000,
            'image_url': 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=400&h=300&fit=crop',
            'sku': 'MSK-SUR-L1-50'
        },
        {
            'name': 'N95 Respirator Masks',
            'category': 'Protective Equipment',
            'description': 'NIOSH-approved N95 respirators providing 95% filtration efficiency',
            'packaging': 'Box of 20',
            'price': 24.99,
            'bulk_price': 22.49,
            'stock_quantity': 500,
            'image_url': 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=400&h=300&fit=crop',
            'sku': 'MSK-N95-20'
        },
        {
            'name': 'Disposable Isolation Gowns',
            'category': 'Protective Equipment',
            'description': 'Fluid-resistant isolation gowns with elastic cuffs and tie closures',
            'packaging': 'Pack of 10',
            'price': 18.99,
            'bulk_price': 17.09,
            'stock_quantity': 300,
            'image_url': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=400&h=300&fit=crop',
            'sku': 'GWN-ISO-10'
        },
        
        # Diagnostic Tools
        {
            'name': 'Digital Thermometer - Oral',
            'category': 'Diagnostic Tools',
            'description': 'Fast and accurate digital thermometer with fever alarm and memory recall',
            'packaging': 'Individual Unit',
            'price': 8.99,
            'bulk_price': 8.09,
            'stock_quantity': 150,
            'image_url': 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=400&h=300&fit=crop',
            'sku': 'THM-DIG-ORL'
        },
        {
            'name': 'Infrared Forehead Thermometer',
            'category': 'Diagnostic Tools',
            'description': 'Non-contact infrared thermometer for quick temperature screening',
            'packaging': 'Individual Unit',
            'price': 34.99,
            'bulk_price': 31.49,
            'stock_quantity': 75,
            'image_url': 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=400&h=300&fit=crop',
            'sku': 'THM-IR-FH'
        },
        {
            'name': 'Stethoscope - Dual Head',
            'category': 'Diagnostic Tools',
            'description': 'Professional dual-head stethoscope with excellent acoustic performance',
            'packaging': 'Individual Unit',
            'price': 45.99,
            'bulk_price': 41.39,
            'stock_quantity': 50,
            'image_url': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop',
            'sku': 'STH-DH-PRO'
        },
        {
            'name': 'Blood Pressure Monitor - Manual',
            'category': 'Diagnostic Tools',
            'description': 'Professional aneroid sphygmomanometer with adult cuff',
            'packaging': 'Individual Unit',
            'price': 28.99,
            'bulk_price': 26.09,
            'stock_quantity': 40,
            'image_url': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop',
            'sku': 'BP-MAN-ADT'
        },
        {
            'name': 'Pulse Oximeter - Fingertip',
            'category': 'Diagnostic Tools',
            'description': 'Portable fingertip pulse oximeter with LED display for SpO2 and pulse rate',
            'packaging': 'Individual Unit',
            'price': 22.99,
            'bulk_price': 20.69,
            'stock_quantity': 100,
            'image_url': 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=400&h=300&fit=crop',
            'sku': 'POX-FT-LED'
        },
        
        # Wound Care
        {
            'name': 'Sterile Gauze Pads 2x2"',
            'category': 'Wound Care',
            'description': 'Sterile gauze pads for wound dressing and general medical use',
            'packaging': 'Box of 100',
            'price': 9.99,
            'bulk_price': 8.99,
            'stock_quantity': 800,
            'image_url': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
            'sku': 'GAU-2X2-100'
        },
        {
            'name': 'Sterile Gauze Pads 4x4"',
            'category': 'Wound Care',
            'description': 'Large sterile gauze pads ideal for larger wounds and surgical sites',
            'packaging': 'Box of 50',
            'price': 12.99,
            'bulk_price': 11.69,
            'stock_quantity': 600,
            'image_url': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
            'sku': 'GAU-4X4-50'
        },
        {
            'name': 'Medical Tape - 1" x 10 yards',
            'category': 'Wound Care',
            'description': 'Hypoallergenic medical tape for securing dressings and devices',
            'packaging': 'Roll',
            'price': 3.99,
            'bulk_price': 3.59,
            'stock_quantity': 500,
            'image_url': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
            'sku': 'TAP-MED-1X10'
        },
        {
            'name': 'Adhesive Bandages - Assorted',
            'category': 'Wound Care',
            'description': 'Assorted adhesive bandages for minor cuts and scrapes',
            'packaging': 'Box of 100',
            'price': 6.99,
            'bulk_price': 6.29,
            'stock_quantity': 1000,
            'image_url': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
            'sku': 'BND-ADH-AST-100'
        },
        {
            'name': 'Elastic Bandages - 3"',
            'category': 'Wound Care',
            'description': 'Elastic compression bandages for sprains and support',
            'packaging': 'Pack of 5',
            'price': 8.99,
            'bulk_price': 8.09,
            'stock_quantity': 200,
            'image_url': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
            'sku': 'BND-ELA-3-5'
        },
        
        # Antiseptics & Solutions
        {
            'name': 'Isopropyl Alcohol 70% - 16oz',
            'category': 'Antiseptics & Solutions',
            'description': 'USP grade isopropyl alcohol for disinfection and cleaning',
            'packaging': '16 fl oz bottle',
            'price': 4.99,
            'bulk_price': 4.49,
            'stock_quantity': 300,
            'image_url': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop',
            'sku': 'ALC-ISO-16OZ'
        },
        {
            'name': 'Betadine Antiseptic Solution - 8oz',
            'category': 'Antiseptics & Solutions',
            'description': 'Povidone iodine antiseptic solution for wound care and surgical prep',
            'packaging': '8 fl oz bottle',
            'price': 12.99,
            'bulk_price': 11.69,
            'stock_quantity': 150,
            'image_url': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop',
            'sku': 'BET-ANT-8OZ'
        },
        {
            'name': 'Alcohol Prep Pads',
            'category': 'Antiseptics & Solutions',
            'description': 'Sterile alcohol prep pads for injection site preparation',
            'packaging': 'Box of 100',
            'price': 5.99,
            'bulk_price': 5.39,
            'stock_quantity': 1500,
            'image_url': 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=400&h=300&fit=crop',
            'sku': 'ALC-PAD-100'
        },
        {
            'name': 'Hydrogen Peroxide 3% - 16oz',
            'category': 'Antiseptics & Solutions',
            'description': 'USP grade hydrogen peroxide for wound cleaning and disinfection',
            'packaging': '16 fl oz bottle',
            'price': 3.99,
            'bulk_price': 3.59,
            'stock_quantity': 250,
            'image_url': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop',
            'sku': 'H2O2-16OZ'
        },
        
        # Surgical Instruments
        {
            'name': 'Surgical Scissors - Straight',
            'category': 'Surgical Instruments',
            'description': 'High-quality stainless steel surgical scissors with straight blades',
            'packaging': 'Individual Unit',
            'price': 24.99,
            'bulk_price': 22.49,
            'stock_quantity': 30,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'SCI-STR-SS'
        },
        {
            'name': 'Surgical Scissors - Curved',
            'category': 'Surgical Instruments',
            'description': 'Precision curved surgical scissors for delicate procedures',
            'packaging': 'Individual Unit',
            'price': 26.99,
            'bulk_price': 24.29,
            'stock_quantity': 25,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'SCI-CUR-SS'
        },
        {
            'name': 'Forceps - Straight',
            'category': 'Surgical Instruments',
            'description': 'Stainless steel straight forceps for grasping and manipulation',
            'packaging': 'Individual Unit',
            'price': 18.99,
            'bulk_price': 17.09,
            'stock_quantity': 40,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'FOR-STR-SS'
        },
        {
            'name': 'Scalpel Handles - #3',
            'category': 'Surgical Instruments',
            'description': 'Reusable scalpel handles compatible with #10-15 blades',
            'packaging': 'Individual Unit',
            'price': 15.99,
            'bulk_price': 14.39,
            'stock_quantity': 35,
            'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
            'sku': 'SCA-HND-3'
        }
    ]
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("🏥 Enhancing Medical Products Database...")
        print("=" * 60)
        
        # Clear existing products
        cursor.execute('DELETE FROM products')
        print("✅ Cleared existing products")
        
        # Insert enhanced medical products
        for i, product in enumerate(medical_products, 1):
            cursor.execute('''
                INSERT INTO products (
                    name, category, description, price, bulk_price, stock_quantity, 
                    image_url, sku, low_stock_threshold, is_active, featured
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product['name'],
                product['category'],
                product['description'],
                product['price'],
                product['bulk_price'],
                product['stock_quantity'],
                product['image_url'],
                product['sku'],
                10,  # low_stock_threshold
                True,  # is_active
                i <= 6  # featured (first 6 products)
            ))
        
        # Add product categories
        categories = [
            ('Syringes & Needles', 'syringes-needles', 'Medical syringes, needles, and injection equipment'),
            ('Protective Equipment', 'protective-equipment', 'PPE including gloves, masks, and gowns'),
            ('Diagnostic Tools', 'diagnostic-tools', 'Medical diagnostic instruments and devices'),
            ('Wound Care', 'wound-care', 'Bandages, dressings, and wound care supplies'),
            ('Antiseptics & Solutions', 'antiseptics-solutions', 'Medical cleaning and antiseptic solutions'),
            ('Surgical Instruments', 'surgical-instruments', 'Professional surgical tools and instruments')
        ]
        
        cursor.execute('DELETE FROM product_categories')
        for i, (name, slug, description) in enumerate(categories, 1):
            cursor.execute('''
                INSERT INTO product_categories (name, slug, description, sort_order, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, slug, description, i, True))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Added {len(medical_products)} professional medical products")
        print(f"✅ Added {len(categories)} product categories")
        print("\n📊 Product Summary by Category:")
        
        # Show summary
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        for category_name, _, _ in categories:
            cursor.execute('SELECT COUNT(*) FROM products WHERE category = ?', (category_name,))
            count = cursor.fetchone()[0]
            print(f"  • {category_name}: {count} products")
        
        conn.close()
        
        print("\n🎉 Medical products database enhanced successfully!")
        
    except Exception as e:
        print(f"❌ Error enhancing products: {str(e)}")

if __name__ == "__main__":
    enhance_medical_products()
