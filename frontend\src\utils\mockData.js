// Mock data for demonstration when backend is not available

export const mockProducts = [
  {
    id: 1,
    name: "Disposable Syringes 10ml",
    description: "Sterile single-use syringes for medical injections",
    packaging: "Box of 100",
    unit_price: 25.99,
    bulk_price: 23.99,
    manufacturer: "MedTech Solutions",
    expiry_date: "2025-12-31",
    stock_quantity: 500,
    reorder_level: 50,
    category: "Syringes",
    image_url: "/images/syringe-10ml.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 2,
    name: "Nitrile Examination Gloves",
    description: "Powder-free nitrile gloves for medical examination",
    packaging: "Box of 100",
    unit_price: 18.50,
    bulk_price: 16.75,
    manufacturer: "SafeGuard Medical",
    expiry_date: "2025-06-30",
    stock_quantity: 800,
    reorder_level: 100,
    category: "Gloves",
    image_url: "/images/nitrile-gloves.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 3,
    name: "Surgical Face Masks",
    description: "3-layer disposable face masks with ear loops",
    packaging: "Box of 50",
    unit_price: 12.99,
    bulk_price: 11.50,
    manufacturer: "ProtectMed",
    expiry_date: "2025-09-15",
    stock_quantity: 1200,
    reorder_level: 150,
    category: "PPE",
    image_url: "/images/face-masks.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 4,
    name: "Digital Thermometer",
    description: "Non-contact infrared thermometer with LCD display",
    packaging: "Individual unit",
    unit_price: 45.00,
    bulk_price: 42.00,
    manufacturer: "TempCheck Pro",
    expiry_date: "2026-03-20",
    stock_quantity: 75,
    reorder_level: 10,
    category: "Instruments",
    image_url: "/images/thermometer.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 5,
    name: "Antiseptic Solution 500ml",
    description: "Povidone iodine antiseptic solution for wound care",
    packaging: "Bottle 500ml",
    unit_price: 8.75,
    bulk_price: 7.99,
    manufacturer: "CleanCare",
    expiry_date: "2025-08-10",
    stock_quantity: 200,
    reorder_level: 25,
    category: "Antiseptics",
    image_url: "/images/antiseptic.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 6,
    name: "Surgical Scissors",
    description: "Stainless steel curved surgical scissors",
    packaging: "Individual unit",
    unit_price: 28.50,
    bulk_price: 26.00,
    manufacturer: "SteelMed",
    expiry_date: "2030-01-01",
    stock_quantity: 45,
    reorder_level: 5,
    category: "Instruments",
    image_url: "/images/scissors.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 7,
    name: "Gauze Bandages 4x4",
    description: "Sterile gauze pads for wound dressing",
    packaging: "Pack of 25",
    unit_price: 15.25,
    bulk_price: 13.75,
    manufacturer: "WoundCare Plus",
    expiry_date: "2025-11-30",
    stock_quantity: 300,
    reorder_level: 40,
    category: "Bandages",
    image_url: "/images/gauze.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  },
  {
    id: 8,
    name: "Alcohol Swabs",
    description: "70% isopropyl alcohol prep pads",
    packaging: "Box of 200",
    unit_price: 9.99,
    bulk_price: 8.99,
    manufacturer: "SterilePad",
    expiry_date: "2025-07-25",
    stock_quantity: 600,
    reorder_level: 75,
    category: "Antiseptics",
    image_url: "/images/alcohol-swabs.jpg",
    is_active: true,
    is_low_stock: false,
    is_near_expiry: false
  }
];

export const mockCategories = [
  "Syringes",
  "Gloves", 
  "PPE",
  "Instruments",
  "Antiseptics",
  "Bandages",
  "Tape"
];

export const mockUser = {
  id: 1,
  email: "<EMAIL>",
  first_name: "John",
  last_name: "Doe",
  phone: "+**********",
  is_admin: false,
  is_verified: true,
  created_at: "2024-01-01T00:00:00"
};

export const mockAdminUser = {
  id: 2,
  email: "<EMAIL>",
  first_name: "Admin",
  last_name: "User",
  phone: "",
  is_admin: true,
  is_verified: true,
  created_at: "2024-01-01T00:00:00"
};

export const mockOrders = [
  {
    id: 1,
    user_id: 1,
    order_number: "GM20240101120000",
    status: "Pending",
    total_amount: 125.99,
    shipping_address: "123 Medical Center Dr\nCity, State 12345",
    notes: "Urgent delivery needed",
    created_at: "2024-01-01T12:00:00",
    updated_at: "2024-01-01T12:00:00",
    items: [
      {
        id: 1,
        product_id: 1,
        quantity: 5,
        unit_price: 25.99,
        total_price: 129.95,
        product: mockProducts[0]
      }
    ],
    user: mockUser
  }
];

// Mock API functions
export const mockAPI = {
  // Auth
  login: async (email, password) => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    
    if (email === "<EMAIL>" && password === "admin123") {
      return {
        success: true,
        data: {
          access_token: "mock-admin-token",
          user: mockAdminUser
        }
      };
    } else if (email === "<EMAIL>" && password === "password123") {
      return {
        success: true,
        data: {
          access_token: "mock-user-token", 
          user: mockUser
        }
      };
    } else {
      return {
        success: false,
        error: "Invalid email or password"
      };
    }
  },

  register: async (userData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      success: true,
      message: "Registration successful. You can now log in."
    };
  },

  // Products
  getProducts: async (params = {}) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredProducts = [...mockProducts];
    
    if (params.category) {
      filteredProducts = filteredProducts.filter(p => p.category === params.category);
    }
    
    if (params.search) {
      const searchLower = params.search.toLowerCase();
      filteredProducts = filteredProducts.filter(p => 
        p.name.toLowerCase().includes(searchLower) || 
        p.description.toLowerCase().includes(searchLower)
      );
    }
    
    const page = params.page || 1;
    const perPage = params.per_page || 20;
    const start = (page - 1) * perPage;
    const end = start + perPage;
    
    return {
      success: true,
      data: {
        products: filteredProducts.slice(start, end),
        total: filteredProducts.length,
        pages: Math.ceil(filteredProducts.length / perPage),
        current_page: page
      }
    };
  },

  getProduct: async (id) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const product = mockProducts.find(p => p.id === parseInt(id));
    
    if (product) {
      return { success: true, data: product };
    } else {
      return { success: false, error: "Product not found" };
    }
  },

  getCategories: async () => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return { success: true, data: mockCategories };
  },

  // Orders
  createOrder: async (orderData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newOrder = {
      id: Date.now(),
      user_id: 1,
      order_number: `GM${Date.now()}`,
      status: "Pending",
      total_amount: orderData.items.reduce((total, item) => {
        const product = mockProducts.find(p => p.id === item.product_id);
        const price = item.quantity >= 10 && product.bulk_price ? product.bulk_price : product.unit_price;
        return total + (price * item.quantity);
      }, 0),
      shipping_address: orderData.shipping_address,
      notes: orderData.notes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      items: orderData.items.map(item => {
        const product = mockProducts.find(p => p.id === item.product_id);
        const price = item.quantity >= 10 && product.bulk_price ? product.bulk_price : product.unit_price;
        return {
          id: Date.now() + Math.random(),
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: price,
          total_price: price * item.quantity,
          product: product
        };
      }),
      user: mockUser
    };
    
    return { success: true, data: newOrder };
  },

  getUserOrders: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      data: {
        orders: mockOrders,
        total: mockOrders.length,
        pages: 1,
        current_page: 1
      }
    };
  },

  getOrder: async (id) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const order = mockOrders.find(o => o.id === parseInt(id));
    
    if (order) {
      return { success: true, data: order };
    } else {
      return { success: false, error: "Order not found" };
    }
  }
};
