#!/usr/bin/env python3
"""
Database Viewer for Grace Medical Limited
View all tables, schemas, and data in your SQLite database
"""

import sqlite3
import os
from datetime import datetime

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"📊 {title}")
    print("=" * 60)

def print_section(title):
    """Print a section header"""
    print(f"\n🔍 {title}")
    print("-" * 40)

def get_database_path():
    """Get the database file path"""
    db_files = []
    
    # Check for different database files
    possible_dbs = [
        'grace_medical.db',
        'grace_medical_enhanced.db',
        'database.db',
        'app.db'
    ]
    
    for db_file in possible_dbs:
        if os.path.exists(db_file):
            db_files.append(db_file)
    
    if not db_files:
        print("❌ No database files found in the backend directory")
        return None
    
    if len(db_files) == 1:
        return db_files[0]
    
    # Multiple databases found, let user choose
    print("📁 Multiple database files found:")
    for i, db_file in enumerate(db_files, 1):
        size = os.path.getsize(db_file)
        modified = datetime.fromtimestamp(os.path.getmtime(db_file))
        print(f"  {i}. {db_file} ({size} bytes, modified: {modified.strftime('%Y-%m-%d %H:%M:%S')})")
    
    while True:
        try:
            choice = int(input("\nSelect database (enter number): ")) - 1
            if 0 <= choice < len(db_files):
                return db_files[choice]
            else:
                print("Invalid choice. Please try again.")
        except ValueError:
            print("Please enter a valid number.")

def view_table_schema(cursor, table_name):
    """View the schema of a specific table"""
    print(f"\n📋 Table: {table_name}")
    print("-" * 30)
    
    # Get table schema
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    if columns:
        print("Columns:")
        for col in columns:
            col_id, name, data_type, not_null, default_val, pk = col
            pk_indicator = " (PRIMARY KEY)" if pk else ""
            not_null_indicator = " NOT NULL" if not_null else ""
            default_indicator = f" DEFAULT {default_val}" if default_val else ""
            print(f"  • {name}: {data_type}{pk_indicator}{not_null_indicator}{default_indicator}")
    
    # Get row count
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    print(f"Total rows: {count}")
    
    return count

def view_table_data(cursor, table_name, limit=5):
    """View sample data from a table"""
    print(f"\n📄 Sample data from {table_name} (showing first {limit} rows):")
    print("-" * 50)
    
    try:
        cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
        rows = cursor.fetchall()
        
        if not rows:
            print("  (No data in this table)")
            return
        
        # Get column names
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [col[1] for col in cursor.fetchall()]
        
        # Print column headers
        print("  " + " | ".join(f"{col[:15]:15}" for col in columns))
        print("  " + "-" * (len(columns) * 17))
        
        # Print data rows
        for row in rows:
            formatted_row = []
            for value in row:
                if value is None:
                    formatted_row.append("NULL".ljust(15))
                else:
                    str_value = str(value)
                    if len(str_value) > 15:
                        str_value = str_value[:12] + "..."
                    formatted_row.append(str_value.ljust(15))
            print("  " + " | ".join(formatted_row))
    
    except Exception as e:
        print(f"  Error reading data: {e}")

def view_database_info(db_path):
    """View complete database information"""
    print_header(f"Database Viewer - {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get database file info
        size = os.path.getsize(db_path)
        modified = datetime.fromtimestamp(os.path.getmtime(db_path))
        
        print(f"📁 Database file: {db_path}")
        print(f"📏 File size: {size:,} bytes ({size/1024:.1f} KB)")
        print(f"📅 Last modified: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        if not tables:
            print("\n❌ No tables found in the database")
            return
        
        print_section(f"Database Tables ({len(tables)} found)")
        
        total_rows = 0
        for table in tables:
            table_name = table[0]
            if table_name.startswith('sqlite_'):
                continue  # Skip system tables
            
            count = view_table_schema(cursor, table_name)
            total_rows += count
        
        print(f"\n📊 Total rows across all tables: {total_rows:,}")
        
        # Show sample data for each table
        print_section("Sample Data from Tables")
        
        for table in tables:
            table_name = table[0]
            if table_name.startswith('sqlite_'):
                continue  # Skip system tables
            
            view_table_data(cursor, table_name)
        
        # Show indexes
        print_section("Database Indexes")
        cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%' ORDER BY tbl_name, name")
        indexes = cursor.fetchall()
        
        if indexes:
            for index in indexes:
                print(f"  • {index[0]} on table {index[1]}")
        else:
            print("  (No custom indexes found)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error accessing database: {e}")

def show_database_tools():
    """Show recommended database management tools"""
    print_header("Recommended Database Management Tools")
    
    print("🔧 For viewing and managing your SQLite database, here are the best tools:")
    print()
    
    print("1. 📱 DB Browser for SQLite (FREE - Recommended)")
    print("   • Download: https://sqlitebrowser.org/dl/")
    print("   • Features: Visual table editor, SQL query tool, data import/export")
    print("   • Platforms: Windows, Mac, Linux")
    print("   • Perfect for beginners and advanced users")
    print()
    
    print("2. 🌐 SQLite Online (FREE - Web-based)")
    print("   • Website: https://sqliteonline.com/")
    print("   • Features: No download required, runs in browser")
    print("   • Upload your .db file directly to the website")
    print()
    
    print("3. 💻 SQLiteStudio (FREE)")
    print("   • Download: https://sqlitestudio.pl/")
    print("   • Features: Advanced SQL editor, plugins, portable")
    print("   • Platforms: Windows, Mac, Linux")
    print()
    
    print("4. 🔍 SQLite Viewer (VS Code Extension)")
    print("   • Install in VS Code: Search for 'SQLite Viewer'")
    print("   • Features: View tables directly in VS Code")
    print("   • Perfect if you're already using VS Code")
    print()
    
    print("5. 📊 DBeaver (FREE Community Edition)")
    print("   • Download: https://dbeaver.io/download/")
    print("   • Features: Professional database tool, supports many databases")
    print("   • Platforms: Windows, Mac, Linux")
    print()

def interactive_query():
    """Allow user to run custom SQL queries"""
    db_path = get_database_path()
    if not db_path:
        return
    
    print_header("Interactive SQL Query Tool")
    print("Enter SQL queries to run against your database.")
    print("Type 'exit' to quit, 'tables' to list tables, 'help' for commands.")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        while True:
            query = input("\nSQL> ").strip()
            
            if query.lower() == 'exit':
                break
            elif query.lower() == 'tables':
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
                tables = cursor.fetchall()
                print("Tables:", [t[0] for t in tables])
                continue
            elif query.lower() == 'help':
                print("Commands:")
                print("  tables - List all tables")
                print("  exit - Quit")
                print("  Any SQL query (SELECT, INSERT, UPDATE, etc.)")
                continue
            elif not query:
                continue
            
            try:
                cursor.execute(query)
                
                if query.lower().startswith('select'):
                    results = cursor.fetchall()
                    if results:
                        # Get column names
                        col_names = [description[0] for description in cursor.description]
                        print("Results:")
                        print(" | ".join(col_names))
                        print("-" * (len(" | ".join(col_names))))
                        for row in results:
                            print(" | ".join(str(cell) for cell in row))
                    else:
                        print("No results found.")
                else:
                    conn.commit()
                    print(f"Query executed successfully. Rows affected: {cursor.rowcount}")
                    
            except Exception as e:
                print(f"Error: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")

def main():
    """Main function"""
    print("🗄️ Grace Medical Limited - Database Viewer")
    print("Choose an option:")
    print("1. View database structure and data")
    print("2. Show database management tools")
    print("3. Interactive SQL query tool")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-3): ").strip()
            
            if choice == '1':
                db_path = get_database_path()
                if db_path:
                    view_database_info(db_path)
                break
            elif choice == '2':
                show_database_tools()
                break
            elif choice == '3':
                interactive_query()
                break
            else:
                print("Please enter 1, 2, or 3")
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break

if __name__ == "__main__":
    main()
