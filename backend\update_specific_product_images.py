#!/usr/bin/env python3
"""
Update specific product images with new URLs as requested by user
"""

import sqlite3
import os

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def update_specific_product_images():
    """Update specific products with new image URLs"""
    
    # Product name to image URL mapping based on user's request
    product_image_mapping = {
        'Disposable Syringes 10ml': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
        'Nitrile Examination Gloves': 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop',
        'Surgical Face Masks': 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop',
        'Digital Thermometer': 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=300&h=200&fit=crop',
        'Antiseptic Solution 500ml': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
        'Surgical Scissors': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
        'Gauze Bandages 4x4': 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
        'Alcohol Swabs': 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop',
        'Blood Pressure Cuff': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=300&h=200&fit=crop'
    }
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("🔄 Updating specific product images...")
        print("=" * 60)
        
        # Get all products to see what we have
        cursor.execute('SELECT id, name, image_url FROM products')
        all_products = cursor.fetchall()
        
        print(f"Found {len(all_products)} total products in database")
        print("\nLooking for specific products to update:")
        
        updated_count = 0
        
        for product_id, product_name, current_url in all_products:
            # Check if this product matches any of our target products
            for target_name, new_url in product_image_mapping.items():
                if target_name.lower() in product_name.lower() or product_name.lower() in target_name.lower():
                    # Update this product
                    cursor.execute('UPDATE products SET image_url = ? WHERE id = ?', (new_url, product_id))
                    
                    print(f"✅ Updated: {product_name}")
                    print(f"   ID: {product_id}")
                    print(f"   Old URL: {current_url}")
                    print(f"   New URL: {new_url}")
                    print()
                    
                    updated_count += 1
                    break
        
        # Also update any products that still have the old problematic URL
        old_url = 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=300&h=200&fit=crop'
        cursor.execute('SELECT id, name FROM products WHERE image_url = ?', (old_url,))
        products_with_old_url = cursor.fetchall()
        
        if products_with_old_url:
            print(f"\nFound {len(products_with_old_url)} products still using old URL:")
            for product_id, product_name in products_with_old_url:
                # Assign appropriate image based on product name
                new_url = get_appropriate_image_url(product_name)
                cursor.execute('UPDATE products SET image_url = ? WHERE id = ?', (new_url, product_id))
                
                print(f"✅ Fixed: {product_name} -> {new_url}")
                updated_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Successfully updated {updated_count} product images!")
        print("All specified products now have new image URLs.")
        
    except Exception as e:
        print(f"❌ Error updating product images: {str(e)}")

def get_appropriate_image_url(product_name):
    """Get appropriate image URL based on product name"""
    name_lower = product_name.lower()
    
    if 'syringe' in name_lower:
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
    elif 'glove' in name_lower:
        return 'https://images.unsplash.com/photo-1584362917165-526a968579e8?w=300&h=200&fit=crop'
    elif 'mask' in name_lower:
        return 'https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=200&fit=crop'
    elif 'thermometer' in name_lower:
        return 'https://images.unsplash.com/photo-1584432810601-6c7f27d2362b?w=300&h=200&fit=crop'
    elif 'antiseptic' in name_lower or 'solution' in name_lower:
        return 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop'
    elif 'scissors' in name_lower:
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'
    elif 'bandage' in name_lower or 'gauze' in name_lower:
        return 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop'
    elif 'swab' in name_lower or 'alcohol' in name_lower:
        return 'https://images.unsplash.com/photo-1584433144859-68e5ac7f9d5c?w=300&h=200&fit=crop'
    elif 'pressure' in name_lower or 'cuff' in name_lower:
        return 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=300&h=200&fit=crop'
    else:
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop'

def list_current_products():
    """List all current products and their image URLs"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, image_url FROM products ORDER BY id')
        products = cursor.fetchall()
        
        print("📋 Current Products and Image URLs:")
        print("=" * 80)
        
        for product_id, name, image_url in products:
            print(f"ID {product_id:2d}: {name[:40]:40} | {image_url}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error listing products: {str(e)}")

if __name__ == "__main__":
    print("🏥 Grace Medical Limited - Specific Product Image Updater")
    print("=" * 70)
    
    # List current products first
    list_current_products()
    
    print("\n" + "=" * 70)
    
    # Update specific products
    update_specific_product_images()
    
    print("\n" + "=" * 70)
    print("✅ Image update process completed!")
