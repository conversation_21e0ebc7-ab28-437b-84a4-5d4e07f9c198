"""
Grace Medical Limited - Enhanced Production Backend
Medical Supply E-commerce Application
Fixed and Enhanced for Production Use
"""

from flask import Flask, request, jsonify, render_template_string, send_file
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import os
import csv
import logging
import sqlite3
from datetime import datetime, date, timedelta
import uuid
import json
from functools import wraps

# Initialize Flask app
app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'grace-medical-secret-key-2024')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///grace_medical_enhanced.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Configure CORS for frontend integration
CORS(app, 
     origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:5173", "http://127.0.0.1:5173"],
     allow_headers=["Content-Type", "Authorization", "X-API-Key"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     supports_credentials=True)

# API Key for admin routes
API_KEY = "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"

# Database file path
DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical_enhanced.db')

def require_api_key(f):
    """Decorator to require API key for admin routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        if api_key != API_KEY:
            return jsonify({'error': 'Invalid API key'}), 401
        return f(*args, **kwargs)
    return decorated_function

def init_database():
    """Initialize the database with enhanced schema"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create users table with enhanced fields
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            phone TEXT,
            role TEXT DEFAULT 'customer',
            is_admin BOOLEAN DEFAULT FALSE,
            is_verified BOOLEAN DEFAULT TRUE,
            is_active BOOLEAN DEFAULT TRUE,
            verification_token TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create products table with enhanced fields
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            category TEXT,
            manufacturer TEXT,
            packaging TEXT,
            unit_price REAL NOT NULL,
            bulk_price REAL,
            stock_quantity INTEGER DEFAULT 0,
            reorder_level INTEGER DEFAULT 10,
            image_url TEXT,
            expiry_date DATE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create orders table with all required fields
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            order_number TEXT UNIQUE NOT NULL,
            status TEXT DEFAULT 'Pending',
            total_amount REAL NOT NULL,
            shipping_address TEXT NOT NULL,
            billing_address TEXT,
            phone TEXT NOT NULL,
            email TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Create order_items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')

    # Create notifications table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type TEXT DEFAULT 'info',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Create categories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create indexes for better performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)')

    conn.commit()
    conn.close()
    logger.info("✅ Database initialized successfully")

def import_products_from_csv():
    """Import products from CSV file ONLY on first run"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Check if products already exist - ONLY import on first run
    cursor.execute('SELECT COUNT(*) FROM products')
    existing_count = cursor.fetchone()[0]
    
    if existing_count > 0:
        logger.info(f"✅ Database already contains {existing_count} products. Skipping CSV import.")
        conn.close()
        return True
    
    logger.info("🔄 First run detected - importing products from CSV...")
    
    csv_path = os.path.join(os.path.dirname(__file__), 'data', 'surgical_supply_catalog_100items.csv')
    
    if not os.path.exists(csv_path):
        logger.warning(f"❌ CSV file not found: {csv_path}")
        logger.info("Creating sample products instead...")
        create_sample_products(cursor)
        conn.commit()
        conn.close()
        return True
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            products_imported = 0
            
            for row in csv_reader:
                try:
                    # Parse and validate data
                    unit_price = float(row.get('unit_price', 0))
                    bulk_price = float(row.get('bulk_price', 0)) if row.get('bulk_price') else None
                    stock_quantity = int(row.get('stock_quantity', 0))
                    reorder_level = int(row.get('reorder_level', 10))
                    
                    # Handle expiry date
                    expiry_date = None
                    if row.get('expiry_date'):
                        try:
                            expiry_date = datetime.strptime(row['expiry_date'], '%Y-%m-%d').date()
                        except ValueError:
                            expiry_date = None
                    
                    # Handle image URL - store NULL if empty, let frontend handle placeholder
                    image_url = row.get('image_url', '').strip()
                    if not image_url or image_url.lower() in ['', 'null', 'none', 'n/a']:
                        image_url = None
                    
                    cursor.execute('''
                        INSERT INTO products (
                            name, description, category, manufacturer, packaging,
                            unit_price, bulk_price, stock_quantity, reorder_level,
                            image_url, expiry_date, is_active
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        row.get('name', '').strip(),
                        row.get('description', '').strip(),
                        row.get('category', 'General').strip(),
                        row.get('manufacturer', '').strip(),
                        row.get('packaging', '').strip(),
                        unit_price,
                        bulk_price,
                        stock_quantity,
                        reorder_level,
                        image_url,
                        expiry_date,
                        True
                    ))
                    products_imported += 1
                    
                except (ValueError, TypeError) as e:
                    logger.warning(f"⚠️ Error importing row {row}: {e}")
                    continue
            
            conn.commit()
            logger.info(f"✅ Successfully imported {products_imported} products from CSV on first run")
            
    except Exception as e:
        logger.error(f"❌ Error reading CSV file: {e}")
        logger.info("Creating sample products instead...")
        create_sample_products(cursor)
        conn.commit()
    
    conn.close()
    return True

def create_sample_products(cursor):
    """Create sample medical products if CSV import fails"""
    sample_products = [
        ("Digital Thermometer", "Accurate digital thermometer for body temperature", "Diagnostics", "MedTech", "Individual", 25.99, 20.99, 50, 10, None, "2025-12-31"),
        ("Blood Pressure Monitor", "Automatic digital blood pressure monitor", "Diagnostics", "HealthCare", "Individual", 89.99, 75.99, 30, 5, None, "2026-06-30"),
        ("Surgical Gloves (Box)", "Latex-free surgical gloves, 100 pieces", "PPE", "SafeMed", "Box of 100", 15.99, 12.99, 100, 20, None, "2024-12-31"),
        ("Face Masks (Pack)", "Disposable medical face masks, 50 pieces", "PPE", "ProtectAll", "Pack of 50", 12.99, 9.99, 200, 50, None, "2025-03-31"),
        ("Stethoscope", "Professional dual-head stethoscope", "Diagnostics", "MedPro", "Individual", 45.99, 39.99, 25, 5, None, None),
    ]
    
    for product in sample_products:
        cursor.execute('''
            INSERT INTO products (
                name, description, category, manufacturer, packaging,
                unit_price, bulk_price, stock_quantity, reorder_level,
                image_url, expiry_date, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (*product, True))
    
    logger.info(f"✅ Created {len(sample_products)} sample products")

def create_default_admin():
    """Create default admin user if none exists"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM users WHERE is_admin = 1')
    if cursor.fetchone()[0] == 0:
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, role, is_admin, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            '<EMAIL>',
            generate_password_hash('admin123'),
            'Admin',
            'User',
            'admin',
            True,
            True
        ))
        conn.commit()
        logger.info("✅ Default admin user created: <EMAIL> / admin123")
    
    conn.close()

# Authentication Routes
@app.route('/api/auth/register', methods=['POST'])
def register():
    """User registration with enhanced validation"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, data['email']):
            return jsonify({'error': 'Invalid email format'}), 400

        # Validate password strength
        if len(data['password']) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check if user already exists
        cursor.execute('SELECT id FROM users WHERE email = ?', (data['email'],))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': 'Email already registered'}), 400

        # Create new user
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, phone, role, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['email'],
            generate_password_hash(data['password']),
            data['first_name'],
            data['last_name'],
            data.get('phone', ''),
            data.get('role', 'customer'),
            True  # Auto-verify for now
        ))

        conn.commit()
        conn.close()

        logger.info(f"✅ New user registered: {data['email']}")

        return jsonify({
            'message': 'Registration successful. You can now log in.',
            'user': {
                'email': data['email'],
                'first_name': data['first_name'],
                'last_name': data['last_name'],
                'role': data.get('role', 'customer')
            }
        }), 201

    except Exception as e:
        logger.error(f"❌ Registration error: {str(e)}")
        return jsonify({'error': 'Registration failed. Please try again.'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """User login with enhanced security"""
    try:
        data = request.get_json()

        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, email, password_hash, first_name, last_name, phone, role, is_admin, is_verified, is_active
            FROM users WHERE email = ?
        ''', (data['email'],))

        user = cursor.fetchone()
        conn.close()

        if not user or not check_password_hash(user[2], data['password']):
            return jsonify({'error': 'Invalid email or password'}), 401

        if not user[8]:  # is_verified
            return jsonify({'error': 'Please verify your email before logging in'}), 403

        if not user[9]:  # is_active
            return jsonify({'error': 'Account is deactivated'}), 403

        # Create access token (simple implementation)
        access_token = f"user_{user[0]}_{uuid.uuid4().hex}"

        user_data = {
            'id': user[0],
            'email': user[1],
            'first_name': user[3],
            'last_name': user[4],
            'phone': user[5],
            'role': user[6],
            'is_admin': bool(user[7])
        }

        logger.info(f"✅ User logged in: {data['email']}")

        return jsonify({
            'message': 'Login successful',
            'access_token': access_token,
            'user': user_data
        }), 200

    except Exception as e:
        logger.error(f"❌ Login error: {str(e)}")
        return jsonify({'error': 'Login failed. Please try again.'}), 500

# Product Routes
@app.route('/api/products', methods=['GET'])
def get_products():
    """Get products with enhanced filtering and pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        search = request.args.get('search')
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Build query with filters
        query = 'SELECT * FROM products WHERE is_active = 1'
        params = []

        if category:
            query += ' AND category = ?'
            params.append(category)

        if search:
            query += ' AND (name LIKE ? OR description LIKE ? OR manufacturer LIKE ?)'
            search_term = f'%{search}%'
            params.extend([search_term, search_term, search_term])

        # Add sorting
        valid_sort_fields = ['name', 'unit_price', 'stock_quantity', 'created_at']
        if sort_by in valid_sort_fields:
            order = 'DESC' if sort_order.lower() == 'desc' else 'ASC'
            query += f' ORDER BY {sort_by} {order}'

        # Get total count for pagination
        count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # Add pagination
        offset = (page - 1) * per_page
        query += ' LIMIT ? OFFSET ?'
        params.extend([per_page, offset])

        cursor.execute(query, params)
        products = cursor.fetchall()

        # Convert to dictionaries with enhanced data
        product_list = []
        for product in products:
            is_low_stock = product[8] <= product[9]  # stock_quantity <= reorder_level

            # Check if near expiry (within 30 days)
            is_near_expiry = False
            if product[11]:  # expiry_date
                try:
                    expiry_date = datetime.strptime(product[11], '%Y-%m-%d').date()
                    is_near_expiry = expiry_date <= date.today() + timedelta(days=30)
                except:
                    pass

            product_dict = {
                'id': product[0],
                'name': product[1],
                'description': product[2],
                'category': product[3],
                'manufacturer': product[4],
                'packaging': product[5],
                'unit_price': product[6],
                'bulk_price': product[7],
                'stock_quantity': product[8],
                'reorder_level': product[9],
                'image_url': product[10],
                'expiry_date': product[11],
                'is_active': bool(product[12]),
                'is_low_stock': is_low_stock,
                'is_near_expiry': is_near_expiry,
                'created_at': product[13],
                'updated_at': product[14]
            }
            product_list.append(product_dict)

        conn.close()

        total_pages = (total + per_page - 1) // per_page

        return jsonify({
            'products': product_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        }), 200

    except Exception as e:
        logger.error(f"❌ Error fetching products: {str(e)}")
        return jsonify({'error': 'Failed to fetch products'}), 500

@app.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    """Get single product with enhanced details"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM products WHERE id = ? AND is_active = 1', (product_id,))
        product = cursor.fetchone()

        if not product:
            conn.close()
            return jsonify({'error': 'Product not found'}), 404

        # Convert to dictionary with enhanced data
        is_low_stock = product[8] <= product[9]
        is_near_expiry = False
        if product[11]:
            try:
                expiry_date = datetime.strptime(product[11], '%Y-%m-%d').date()
                is_near_expiry = expiry_date <= date.today() + timedelta(days=30)
            except:
                pass

        product_dict = {
            'id': product[0],
            'name': product[1],
            'description': product[2],
            'category': product[3],
            'manufacturer': product[4],
            'packaging': product[5],
            'unit_price': product[6],
            'bulk_price': product[7],
            'stock_quantity': product[8],
            'reorder_level': product[9],
            'image_url': product[10],
            'expiry_date': product[11],
            'is_active': bool(product[12]),
            'is_low_stock': is_low_stock,
            'is_near_expiry': is_near_expiry,
            'created_at': product[13],
            'updated_at': product[14]
        }

        conn.close()
        return jsonify(product_dict), 200

    except Exception as e:
        logger.error(f"❌ Error fetching product {product_id}: {str(e)}")
        return jsonify({'error': 'Failed to fetch product'}), 500

# Order Routes
@app.route('/api/orders', methods=['POST'])
def create_order():
    """Create new order with enhanced validation and notifications"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['items', 'shipping_address', 'phone', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        if not data['items'] or len(data['items']) == 0:
            return jsonify({'error': 'Order must contain at least one item'}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Generate order number
        order_number = f"GM{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6].upper()}"

        # Calculate total and validate stock
        total_amount = 0
        order_items_data = []

        for item in data['items']:
            if not all(k in item for k in ['product_id', 'quantity']):
                conn.close()
                return jsonify({'error': 'Invalid item data'}), 400

            # Get product details
            cursor.execute('SELECT * FROM products WHERE id = ? AND is_active = 1', (item['product_id'],))
            product = cursor.fetchone()

            if not product:
                conn.close()
                return jsonify({'error': f'Product {item["product_id"]} not found'}), 404

            if product[8] < item['quantity']:  # stock_quantity
                conn.close()
                return jsonify({'error': f'Insufficient stock for {product[1]}. Available: {product[8]}'}), 400

            # Calculate price (use bulk price if available and quantity >= 10)
            unit_price = product[7] if product[7] and item['quantity'] >= 10 else product[6]
            item_total = unit_price * item['quantity']
            total_amount += item_total

            order_items_data.append({
                'product_id': item['product_id'],
                'product_name': product[1],
                'quantity': item['quantity'],
                'unit_price': unit_price,
                'total_price': item_total
            })

        # Create order
        cursor.execute('''
            INSERT INTO orders (
                user_id, order_number, status, total_amount,
                shipping_address, phone, email, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('user_id', 1),  # Default user for now
            order_number,
            'Pending',
            total_amount,
            data['shipping_address'],
            data['phone'],
            data['email'],
            data.get('notes', '')
        ))

        order_id = cursor.lastrowid

        # Create order items and update stock
        for item_data in order_items_data:
            cursor.execute('''
                INSERT INTO order_items (
                    order_id, product_id, quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?)
            ''', (
                order_id,
                item_data['product_id'],
                item_data['quantity'],
                item_data['unit_price'],
                item_data['total_price']
            ))

            # Update product stock
            cursor.execute('''
                UPDATE products
                SET stock_quantity = stock_quantity - ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (item_data['quantity'], item_data['product_id']))

        conn.commit()
        conn.close()

        # Send email notification (mock implementation)
        send_order_notification_email({
            'order_number': order_number,
            'total_amount': total_amount,
            'customer_email': data['email'],
            'customer_phone': data['phone'],
            'shipping_address': data['shipping_address'],
            'items': order_items_data
        })

        logger.info(f"✅ Order created: {order_number}")

        return jsonify({
            'message': 'Order placed successfully',
            'order_id': order_id,
            'order_number': order_number,
            'total_amount': total_amount,
            'status': 'Pending'
        }), 201

    except Exception as e:
        logger.error(f"❌ Error creating order: {str(e)}")
        return jsonify({'error': 'Failed to create order. Please try again.'}), 500

@app.route('/api/orders', methods=['GET'])
def get_orders():
    """Get orders with pagination and filtering"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status')
        user_id = request.args.get('user_id', type=int)

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Build query
        query = '''
            SELECT o.*, COUNT(oi.id) as item_count
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE 1=1
        '''
        params = []

        if status:
            query += ' AND o.status = ?'
            params.append(status)

        if user_id:
            query += ' AND o.user_id = ?'
            params.append(user_id)

        query += ' GROUP BY o.id ORDER BY o.created_at DESC'

        # Get total count
        count_query = f"SELECT COUNT(*) FROM ({query}) as subquery"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # Add pagination
        offset = (page - 1) * per_page
        query += ' LIMIT ? OFFSET ?'
        params.extend([per_page, offset])

        cursor.execute(query, params)
        orders = cursor.fetchall()

        order_list = []
        for order in orders:
            order_dict = {
                'id': order[0],
                'user_id': order[1],
                'order_number': order[2],
                'status': order[3],
                'total_amount': order[4],
                'shipping_address': order[5],
                'billing_address': order[6],
                'phone': order[7],
                'email': order[8],
                'notes': order[9],
                'created_at': order[10],
                'updated_at': order[11],
                'item_count': order[12]
            }
            order_list.append(order_dict)

        conn.close()

        total_pages = (total + per_page - 1) // per_page

        return jsonify({
            'orders': order_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        }), 200

    except Exception as e:
        logger.error(f"❌ Error fetching orders: {str(e)}")
        return jsonify({'error': 'Failed to fetch orders'}), 500

@app.route('/api/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    """Get single order with items"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get order details
        cursor.execute('SELECT * FROM orders WHERE id = ?', (order_id,))
        order = cursor.fetchone()

        if not order:
            conn.close()
            return jsonify({'error': 'Order not found'}), 404

        # Get order items with product details
        cursor.execute('''
            SELECT oi.*, p.name, p.image_url
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ''', (order_id,))

        items = []
        for item in cursor.fetchall():
            items.append({
                'id': item[0],
                'product_id': item[2],
                'product_name': item[6],
                'product_image': item[7],
                'quantity': item[3],
                'unit_price': item[4],
                'total_price': item[5]
            })

        order_dict = {
            'id': order[0],
            'user_id': order[1],
            'order_number': order[2],
            'status': order[3],
            'total_amount': order[4],
            'shipping_address': order[5],
            'billing_address': order[6],
            'phone': order[7],
            'email': order[8],
            'notes': order[9],
            'created_at': order[10],
            'updated_at': order[11],
            'items': items
        }

        conn.close()
        return jsonify(order_dict), 200

    except Exception as e:
        logger.error(f"❌ Error fetching order {order_id}: {str(e)}")
        return jsonify({'error': 'Failed to fetch order'}), 500

# Utility Functions
def send_order_notification_email(order_data):
    """Send email notification for new order (mock implementation)"""
    try:
        logger.info(f"📧 NEW ORDER EMAIL NOTIFICATION:")
        logger.info(f"To: <EMAIL>")
        logger.info(f"Subject: 🛒 New Order Received - #{order_data['order_number']}")
        logger.info(f"Order Total: ${order_data['total_amount']}")
        logger.info(f"Customer: {order_data['customer_email']}")
        logger.info(f"Phone: {order_data['customer_phone']}")
        logger.info(f"Address: {order_data['shipping_address']}")
        logger.info(f"Items: {len(order_data['items'])} items")
        logger.info("=" * 50)
        return True
    except Exception as e:
        logger.error(f"❌ Error sending email notification: {str(e)}")
        return False

def check_low_stock_and_expiry():
    """Check for low stock and near-expiry items"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check low stock items
        cursor.execute('''
            SELECT name, stock_quantity, reorder_level
            FROM products
            WHERE stock_quantity <= reorder_level AND is_active = 1
        ''')
        low_stock_items = cursor.fetchall()

        # Check near-expiry items (within 30 days)
        cursor.execute('''
            SELECT name, expiry_date
            FROM products
            WHERE expiry_date IS NOT NULL
            AND date(expiry_date) <= date('now', '+30 days')
            AND is_active = 1
        ''')
        near_expiry_items = cursor.fetchall()

        conn.close()

        # Log alerts
        if low_stock_items:
            logger.warning("🚨 LOW STOCK ALERT:")
            for item in low_stock_items:
                logger.warning(f"- {item[0]}: {item[1]} units (reorder level: {item[2]})")

        if near_expiry_items:
            logger.warning("⚠️ NEAR EXPIRY ALERT:")
            for item in near_expiry_items:
                logger.warning(f"- {item[0]}: expires on {item[1]}")

        if not low_stock_items and not near_expiry_items:
            logger.info("✅ All products are well stocked and not near expiry")

        return {
            'low_stock_items': [{'name': item[0], 'stock_quantity': item[1], 'reorder_level': item[2]} for item in low_stock_items],
            'near_expiry_items': [{'name': item[0], 'expiry_date': item[1]} for item in near_expiry_items]
        }

    except Exception as e:
        logger.error(f"❌ Error checking stock and expiry: {str(e)}")
        return None

# Admin Routes
@app.route('/api/admin/stock-alerts', methods=['GET'])
@require_api_key
def get_stock_alerts():
    """Get stock alerts for admin"""
    try:
        alerts = check_low_stock_and_expiry()
        return jsonify(alerts), 200
    except Exception as e:
        logger.error(f"❌ Error getting stock alerts: {str(e)}")
        return jsonify({'error': 'Failed to get stock alerts'}), 500

@app.route('/api/admin/run-daily-cron', methods=['POST'])
@require_api_key
def run_daily_cron():
    """Manual trigger for daily cron jobs"""
    try:
        logger.info("🕐 Running daily cron jobs...")
        alerts = check_low_stock_and_expiry()

        # Send daily alert email if there are alerts
        if alerts and (alerts['low_stock_items'] or alerts['near_expiry_items']):
            send_daily_alert_email(alerts)

        logger.info("✅ Daily cron jobs completed")
        return jsonify({'message': 'Daily cron jobs executed successfully', 'alerts': alerts}), 200
    except Exception as e:
        logger.error(f"❌ Error running daily cron: {str(e)}")
        return jsonify({'error': 'Failed to run daily cron jobs'}), 500

def send_daily_alert_email(alerts):
    """Send daily stock alert email"""
    try:
        logger.info("📧 DAILY STOCK ALERT EMAIL:")
        logger.info(f"To: <EMAIL>")
        logger.info(f"Subject: Daily Stock & Expiry Alert - Grace Medical Limited")

        if alerts['low_stock_items']:
            logger.info(f"🚨 LOW STOCK ITEMS ({len(alerts['low_stock_items'])}):")
            for item in alerts['low_stock_items']:
                logger.info(f"- {item['name']}: {item['stock_quantity']} units")

        if alerts['near_expiry_items']:
            logger.info(f"⚠️ NEAR EXPIRY ITEMS ({len(alerts['near_expiry_items'])}):")
            for item in alerts['near_expiry_items']:
                logger.info(f"- {item['name']}: expires on {item['expiry_date']}")

        logger.info("=" * 50)
        return True
    except Exception as e:
        logger.error(f"❌ Error sending daily alert email: {str(e)}")
        return False

# Categories Route
@app.route('/api/categories', methods=['GET'])
def get_categories():
    """Get all product categories"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND is_active = 1')
        categories = [row[0] for row in cursor.fetchall()]

        conn.close()
        return jsonify(categories), 200

    except Exception as e:
        logger.error(f"❌ Error fetching categories: {str(e)}")
        return jsonify({'error': 'Failed to fetch categories'}), 500

# Health Check Route
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    }), 200

# Main route
@app.route('/')
def index():
    """Serve basic info page"""
    return jsonify({
        'message': 'Grace Medical Limited - Enhanced Backend API',
        'version': '1.0.0',
        'endpoints': {
            'auth': ['/api/auth/register', '/api/auth/login'],
            'products': ['/api/products', '/api/products/<id>'],
            'orders': ['/api/orders', '/api/orders/<id>'],
            'admin': ['/api/admin/stock-alerts', '/api/admin/run-daily-cron'],
            'utilities': ['/api/categories', '/api/health']
        },
        'documentation': 'See API_DOCUMENTATION.md for detailed usage'
    }), 200

if __name__ == '__main__':
    # Initialize database and import data
    logger.info("🔄 Initializing Grace Medical Limited Enhanced Backend...")

    init_database()
    create_default_admin()
    import_products_from_csv()

    # Run initial stock check
    logger.info("🕐 Running initial stock check...")
    check_low_stock_and_expiry()

    logger.info("\n🚀 Grace Medical Limited Enhanced Backend Server")
    logger.info("=" * 60)
    logger.info("✅ Features Implemented:")
    logger.info("🔐 Enhanced authentication with validation")
    logger.info("📦 Product catalog with advanced filtering")
    logger.info("🛒 Complete order management system")
    logger.info("📧 Email notifications for orders and alerts")
    logger.info("⚠️ Daily stock and expiry monitoring")
    logger.info("🔍 Advanced search and pagination")
    logger.info("🛡️ Enhanced error handling and logging")
    logger.info("🔑 API key protection for admin routes")
    logger.info("=" * 60)
    logger.info("🌐 Server starting at: http://localhost:5000")
    logger.info("🔑 Admin login: <EMAIL> / admin123")
    logger.info("🔐 API Key: " + API_KEY[:20] + "...")
    logger.info("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
