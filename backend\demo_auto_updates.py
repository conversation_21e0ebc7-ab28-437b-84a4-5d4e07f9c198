#!/usr/bin/env python3
"""
Demonstration of automatic order status updates
"""

from order_status_manager import order_status_manager
import sqlite3
import os
from datetime import datetime, timedelta

DB_PATH = os.path.join(os.path.dirname(__file__), 'grace_medical.db')

def demo_auto_updates():
    """Demonstrate automatic status updates"""
    print("🏥 Grace Medical Limited - Auto Status Update Demo")
    print("=" * 60)
    
    try:
        # Show orders eligible for auto-transition
        print("🔍 Checking for orders eligible for automatic status updates...")
        orders_to_update = order_status_manager.get_orders_for_auto_transition()
        
        if orders_to_update:
            print(f"\n📋 Found {len(orders_to_update)} orders ready for auto-transition:")
            print("-" * 50)
            
            for order_id, current_status, next_status in orders_to_update:
                print(f"Order {order_id}: {current_status} → {next_status}")
            
            print(f"\n🔄 Running automatic status updates...")
            order_status_manager.run_auto_status_updates()
            print("✅ Automatic updates completed!")
            
            # Show updated status
            print(f"\n📊 Updated order statuses:")
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            for order_id, old_status, new_status in orders_to_update:
                cursor.execute('SELECT status FROM orders WHERE id = ?', (order_id,))
                current_status = cursor.fetchone()[0]
                print(f"  Order {order_id}: {old_status} → {current_status}")
            
            conn.close()
            
        else:
            print("ℹ️ No orders currently eligible for automatic status updates")
            print("   (Orders need to be in their current status for the configured time period)")
            
            # Show current order statuses
            print(f"\n📋 Current order statuses:")
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, order_number, status, status_updated_at
                FROM orders 
                ORDER BY status_updated_at DESC 
                LIMIT 10
            ''')
            
            orders = cursor.fetchall()
            for order_id, order_number, status, updated_at in orders:
                time_diff = datetime.now() - datetime.fromisoformat(updated_at)
                hours_ago = int(time_diff.total_seconds() / 3600)
                print(f"  Order {order_id} ({order_number}): {status} (updated {hours_ago}h ago)")
            
            conn.close()
        
        print(f"\n🎯 Automatic Status Update Rules:")
        print("  • Pending → Processing: After 2 hours")
        print("  • Processing → Shipped: After 24 hours")
        print("  • Shipped → Complete: After 72 hours")
        
        print(f"\n📧 Email Notifications:")
        print("  • Customers receive emails for each status change")
        print("  • Admins receive alerts for all status updates")
        print("  • Check console output for email notification logs")
        
    except Exception as e:
        print(f"❌ Error in demo: {e}")

if __name__ == "__main__":
    demo_auto_updates()
    
    print(f"\n🌐 To see the enhanced order management:")
    print("1. Open http://127.0.0.1:5000 in your browser")
    print("2. Login as admin: <EMAIL> / admin123")
    print("3. Navigate to 'My Orders' to see enhanced status display")
    print("4. Go to Admin Dashboard → Orders for manual status management")
    print("5. Try updating order statuses manually to see the system in action")
